#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح نافذة التفعيل - التأكد من ظهور حقل الإدخال
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_activation_window():
    """اختبار نافذة التفعيل المصححة"""
    print("🔧 اختبار إصلاح نافذة التفعيل")
    print("=" * 50)
    
    try:
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        app.setLayoutDirection(2)  # RTL
        
        # التأكد من عدم وجود ترخيص
        license_files = ["license.dat", "used_codes.dat"]
        for file in license_files:
            if os.path.exists(file):
                print(f"⚠️ تم العثور على {file} - سيتم حذفه للاختبار")
                os.remove(file)
        
        print("✅ تم حذف ملفات التراخيص - البداية من الصفر")
        
        # استيراد نافذة التفعيل
        from gui.activation_dialog import ActivationDialog
        
        # إنشاء النافذة
        dialog = ActivationDialog()
        
        print(f"✅ تم إنشاء نافذة التفعيل")
        print(f"📏 حجم النافذة: {dialog.size().width()} x {dialog.size().height()}")
        
        # فحص حقل الإدخال
        if hasattr(dialog, 'activation_code'):
            print(f"✅ حقل كود التفعيل موجود")
            print(f"👁️ مرئي: {dialog.activation_code.isVisible()}")
            print(f"📝 النص التوضيحي: {dialog.activation_code.placeholderText()}")
            print(f"📏 حجم الحقل: {dialog.activation_code.size().width()} x {dialog.activation_code.size().height()}")
        else:
            print(f"❌ حقل كود التفعيل غير موجود!")
            return False
        
        # فحص الأزرار
        if hasattr(dialog, 'activate_button'):
            print(f"✅ زر التفعيل موجود: {dialog.activate_button.text()}")
        else:
            print(f"❌ زر التفعيل غير موجود!")
        
        # فحص معلومات الترخيص
        from license_manager import LicenseManager
        lm = LicenseManager()
        status = lm.check_license()
        
        print(f"\n📊 حالة الترخيص:")
        print(f"   - صالح: {status['valid']}")
        print(f"   - الحالة: {status['status']}")
        print(f"   - الرسالة: {status['message']}")
        
        if not status["valid"]:
            print(f"✅ البرنامج غير مفعل - يجب أن يظهر حقل الإدخال")
            
            # التأكد من أن حقل الإدخال مرئي
            if dialog.activation_code.isVisible():
                print(f"✅ حقل الإدخال مرئي كما متوقع")
            else:
                print(f"❌ حقل الإدخال مخفي - هناك مشكلة!")
                return False
        
        # عرض النافذة
        print(f"\n🖥️ عرض النافذة للفحص البصري...")
        print(f"   📋 تحقق من:")
        print(f"   - ظهور رسالة 'البرنامج غير مفعل'")
        print(f"   - عرض بيانات الجهاز")
        print(f"   - ظهور حقل 'كود التفعيل'")
        print(f"   - ظهور زر 'تفعيل البرنامج'")
        print(f"   - ظهور زر 'نسخ بيانات الجهاز'")
        print(f"\n   (اضغط Ctrl+C لإغلاق النافذة)")
        
        dialog.show()
        
        # تشغيل التطبيق
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النافذة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_license_manager():
    """اختبار مدير التراخيص"""
    print(f"\n🔍 اختبار مدير التراخيص")
    print("=" * 30)
    
    try:
        from license_manager import LicenseManager
        
        lm = LicenseManager()
        
        # الحصول على بيانات الجهاز
        customer_code = lm.get_customer_code()
        machine_id = lm.get_machine_id()
        
        print(f"🔑 كود العميل: {customer_code}")
        print(f"💻 رقم الجهاز: {machine_id}")
        
        # فحص الترخيص
        status = lm.check_license()
        
        print(f"\n📊 تفاصيل حالة الترخيص:")
        for key, value in status.items():
            print(f"   - {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مدير التراخيص: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار إصلاح نافذة التفعيل")
    print("=" * 60)
    
    # اختبار مدير التراخيص أولاً
    if not test_license_manager():
        print("❌ فشل في اختبار مدير التراخيص")
        return 1
    
    # اختبار النافذة
    print(f"\n" + "="*60)
    result = test_activation_window()
    
    if result == 0:
        print("\n✅ تم إغلاق النافذة بنجاح")
        print("🎉 إذا ظهر حقل الإدخال، فالإصلاح نجح!")
    else:
        print(f"\n⚠️ النافذة أُغلقت بالكود: {result}")
    
    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n⚠️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
