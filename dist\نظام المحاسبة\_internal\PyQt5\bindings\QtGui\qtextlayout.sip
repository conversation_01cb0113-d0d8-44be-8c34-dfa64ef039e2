// qtextlayout.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QTextInlineObject
{
%TypeHeaderCode
#include <qtextlayout.h>
%End

public:
    bool isValid() const;
    QRectF rect() const;
    qreal width() const;
    qreal ascent() const;
    qreal descent() const;
    qreal height() const;
    Qt::LayoutDirection textDirection() const;
    void setWidth(qreal w);
    void setAscent(qreal a);
    void setDescent(qreal d);
    int textPosition() const;
    int formatIndex() const;
    QTextFormat format() const;
};

class QTextLayout
{
%TypeHeaderCode
#include <qtextlayout.h>
%End

public:
    QTextLayout();
    QTextLayout(const QString &text);
    QTextLayout(const QString &text, const QFont &font, QPaintDevice *paintDevice = 0);
    QTextLayout(const QTextBlock &b);
    ~QTextLayout();
    void setFont(const QFont &f);
    QFont font() const;
    void setText(const QString &string);
    QString text() const;
    void setTextOption(const QTextOption &option);
    const QTextOption &textOption() const;
    void setPreeditArea(int position, const QString &text);
    int preeditAreaPosition() const;
    QString preeditAreaText() const;

    struct FormatRange
    {
%TypeHeaderCode
#include <qtextlayout.h>
%End

        int start;
        int length;
        QTextCharFormat format;
    };

    void setAdditionalFormats(const QList<QTextLayout::FormatRange> &overrides);
    QList<QTextLayout::FormatRange> additionalFormats() const;
    void clearAdditionalFormats();
    void setCacheEnabled(bool enable);
    bool cacheEnabled() const;
    void beginLayout();
    void endLayout();
    QTextLine createLine();
    int lineCount() const;
    QTextLine lineAt(int i) const;
    QTextLine lineForTextPosition(int pos) const;

    enum CursorMode
    {
        SkipCharacters,
        SkipWords,
    };

    bool isValidCursorPosition(int pos) const;
    int nextCursorPosition(int oldPos, QTextLayout::CursorMode mode = QTextLayout::SkipCharacters) const;
    int previousCursorPosition(int oldPos, QTextLayout::CursorMode mode = QTextLayout::SkipCharacters) const;
    void draw(QPainter *p, const QPointF &pos, const QVector<QTextLayout::FormatRange> &selections = QVector<QTextLayout::FormatRange>(), const QRectF &clip = QRectF()) const;
    void drawCursor(QPainter *p, const QPointF &pos, int cursorPosition) const;
    void drawCursor(QPainter *p, const QPointF &pos, int cursorPosition, int width) const;
    QPointF position() const;
    void setPosition(const QPointF &p);
    QRectF boundingRect() const;
    qreal minimumWidth() const;
    qreal maximumWidth() const;
    void clearLayout();
    void setCursorMoveStyle(Qt::CursorMoveStyle style);
    Qt::CursorMoveStyle cursorMoveStyle() const;
    int leftCursorPosition(int oldPos) const;
    int rightCursorPosition(int oldPos) const;
%If (PyQt_RawFont)
    QList<QGlyphRun> glyphRuns(int from = -1, int length = -1) const;
%End
%If (Qt_5_6_0 -)
    void setFormats(const QVector<QTextLayout::FormatRange> &overrides);
%End
%If (Qt_5_6_0 -)
    QVector<QTextLayout::FormatRange> formats() const;
%End
%If (Qt_5_6_0 -)
    void clearFormats();
%End

private:
    QTextLayout(const QTextLayout &);
};

class QTextLine
{
%TypeHeaderCode
#include <qtextlayout.h>
%End

public:
    QTextLine();
    bool isValid() const;
    QRectF rect() const;
    qreal x() const;
    qreal y() const;
    qreal width() const;
    qreal ascent() const;
    qreal descent() const;
    qreal height() const;
    qreal naturalTextWidth() const;
    QRectF naturalTextRect() const;

    enum Edge
    {
        Leading,
        Trailing,
    };

    enum CursorPosition
    {
        CursorBetweenCharacters,
        CursorOnCharacter,
    };

    qreal cursorToX(int *cursorPos /In,Out/, QTextLine::Edge edge = QTextLine::Leading) const;
    int xToCursor(qreal x, QTextLine::CursorPosition edge = QTextLine::CursorBetweenCharacters) const;
    void setLineWidth(qreal width);
    void setNumColumns(int columns);
    void setNumColumns(int columns, qreal alignmentWidth);
    void setPosition(const QPointF &pos);
    int textStart() const;
    int textLength() const;
    int lineNumber() const;
    void draw(QPainter *painter, const QPointF &position, const QTextLayout::FormatRange *selection = 0) const;
    QPointF position() const;
    qreal leading() const;
    void setLeadingIncluded(bool included);
    bool leadingIncluded() const;
    qreal horizontalAdvance() const;
%If (PyQt_RawFont)
    QList<QGlyphRun> glyphRuns(int from = -1, int length = -1) const;
%End
};

%If (Qt_5_6_0 -)
bool operator==(const QTextLayout::FormatRange &lhs, const QTextLayout::FormatRange &rhs);
%End
%If (Qt_5_6_0 -)
bool operator!=(const QTextLayout::FormatRange &lhs, const QTextLayout::FormatRange &rhs);
%End
