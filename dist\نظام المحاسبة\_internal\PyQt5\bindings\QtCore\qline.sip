// qline.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QLine
{
%TypeHeaderCode
#include <qline.h>
%End

%PickleCode
    sipRes = Py_BuildValue((char *)"iiii", sipCpp->x1(), sipCpp->y1(), sipCpp->x2(), sipCpp->y2());
%End

public:
    bool operator!=(const QLine &d) const;
    QLine();
    QLine(const QPoint &pt1_, const QPoint &pt2_);
    QLine(int x1pos, int y1pos, int x2pos, int y2pos);
    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        if (sipCpp->isNull())
        {
        #if PY_MAJOR_VERSION >= 3
            sipRes = PyUnicode_FromString("PyQt5.QtCore.QLine()");
        #else
            sipRes = PyString_FromString("PyQt5.QtCore.QLine()");
        #endif
        }
        else
        {
            sipRes =
        #if PY_MAJOR_VERSION >= 3
                PyUnicode_FromFormat
        #else
                PyString_FromFormat
        #endif
                    ("PyQt5.QtCore.QLine(%i, %i, %i, %i)",
                    sipCpp->x1(), sipCpp->y1(), sipCpp->x2(), sipCpp->y2());
        }
%End

    bool isNull() const;
    int __bool__() const;
%MethodCode
        sipRes = !sipCpp->isNull();
%End

    int x1() const;
    int y1() const;
    int x2() const;
    int y2() const;
    QPoint p1() const;
    QPoint p2() const;
    int dx() const;
    int dy() const;
    void translate(const QPoint &point);
    void translate(int adx, int ady);
    bool operator==(const QLine &d) const;
    QLine translated(const QPoint &p) const;
    QLine translated(int adx, int ady) const;
    void setP1(const QPoint &aP1);
    void setP2(const QPoint &aP2);
    void setPoints(const QPoint &aP1, const QPoint &aP2);
    void setLine(int aX1, int aY1, int aX2, int aY2);
%If (Qt_5_8_0 -)
    QPoint center() const;
%End
};

QDataStream &operator<<(QDataStream &, const QLine & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QLine & /Constrained/) /ReleaseGIL/;

class QLineF
{
%TypeHeaderCode
#include <qline.h>
%End

%PickleCode
    sipRes = Py_BuildValue((char *)"dddd", sipCpp->x1(), sipCpp->y1(), sipCpp->x2(), sipCpp->y2());
%End

public:
    enum IntersectType
    {
        NoIntersection,
        BoundedIntersection,
        UnboundedIntersection,
    };

    QLineF(const QLine &line);
    bool isNull() const;
    int __bool__() const;
%MethodCode
        sipRes = !sipCpp->isNull();
%End

    qreal length() const;
    QLineF unitVector() const;
    QLineF::IntersectType intersect(const QLineF &l, QPointF *intersectionPoint) const;
%If (Qt_5_14_0 -)
    typedef QLineF::IntersectType IntersectionType;
%End
%If (Qt_5_14_0 -)
    QLineF::IntersectionType intersects(const QLineF &l, QPointF *intersectionPoint /Out/) const;
%End
    bool operator!=(const QLineF &d) const;
    QLineF();
    QLineF(const QPointF &apt1, const QPointF &apt2);
    QLineF(qreal x1pos, qreal y1pos, qreal x2pos, qreal y2pos);
    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        if (sipCpp->isNull())
        {
        #if PY_MAJOR_VERSION >= 3
            sipRes = PyUnicode_FromString("PyQt5.QtCore.QLineF()");
        #else
            sipRes = PyString_FromString("PyQt5.QtCore.QLineF()");
        #endif
        }
        else
        {
            PyObject *x1 = PyFloat_FromDouble(sipCpp->x1());
            PyObject *y1 = PyFloat_FromDouble(sipCpp->y1());
            PyObject *x2 = PyFloat_FromDouble(sipCpp->x2());
            PyObject *y2 = PyFloat_FromDouble(sipCpp->y2());
        
            if (x1 && y1 && x2 && y2)
            {
        #if PY_MAJOR_VERSION >= 3
                sipRes = PyUnicode_FromFormat("PyQt5.QtCore.QLineF(%R, %R, %R, %R)",
                        x1, y1, x2, y2);
        #else
                sipRes = PyString_FromString("PyQt5.QtCore.QLineF(");
                PyString_ConcatAndDel(&sipRes, PyObject_Repr(x1));
                PyString_ConcatAndDel(&sipRes, PyString_FromString(", "));
                PyString_ConcatAndDel(&sipRes, PyObject_Repr(y1));
                PyString_ConcatAndDel(&sipRes, PyString_FromString(", "));
                PyString_ConcatAndDel(&sipRes, PyObject_Repr(x2));
                PyString_ConcatAndDel(&sipRes, PyString_FromString(", "));
                PyString_ConcatAndDel(&sipRes, PyObject_Repr(y2));
                PyString_ConcatAndDel(&sipRes, PyString_FromString(")"));
        #endif
            }
        
            Py_XDECREF(x1);
            Py_XDECREF(y1);
            Py_XDECREF(x2);
            Py_XDECREF(y2);
        }
%End

    qreal x1() const;
    qreal y1() const;
    qreal x2() const;
    qreal y2() const;
    QPointF p1() const;
    QPointF p2() const;
    qreal dx() const;
    qreal dy() const;
    QLineF normalVector() const;
    void translate(const QPointF &point);
    void translate(qreal adx, qreal ady);
    void setLength(qreal len);
    QPointF pointAt(qreal t) const;
    QLine toLine() const;
    bool operator==(const QLineF &d) const;
    static QLineF fromPolar(qreal length, qreal angle);
    qreal angle() const;
    void setAngle(qreal angle);
    qreal angleTo(const QLineF &l) const;
    QLineF translated(const QPointF &p) const;
    QLineF translated(qreal adx, qreal ady) const;
    void setP1(const QPointF &aP1);
    void setP2(const QPointF &aP2);
    void setPoints(const QPointF &aP1, const QPointF &aP2);
    void setLine(qreal aX1, qreal aY1, qreal aX2, qreal aY2);
%If (Qt_5_8_0 -)
    QPointF center() const;
%End
};

QDataStream &operator<<(QDataStream &, const QLineF & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QLineF & /Constrained/) /ReleaseGIL/;
