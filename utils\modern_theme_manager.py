#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الثيمات الحديثة والعصرية للتطبيق
"""

import json
import os
from typing import Dict, Any, List

class ModernThemeManager:
    """مدير الثيمات الحديثة"""
    
    def __init__(self):
        self.settings_file = "modern_theme_settings.json"
        self.current_theme = "light_modern"
        self.load_theme_preference()
    
    def get_available_themes(self) -> Dict[str, Dict[str, Any]]:
        """الحصول على جميع الثيمات المتاحة"""
        return {
            "light_modern": {
                "name": "🌟 مودرن فاتح",
                "description": "ثيم عصري بألوان فاتحة ومتناسقة",
                "colors": self.get_light_modern_colors()
            },
            "dark_elegant": {
                "name": "🌙 أنيق داكن", 
                "description": "ثيم داكن أنيق مع لمسات ذهبية",
                "colors": self.get_dark_elegant_colors()
            },
            "ocean_blue": {
                "name": "🌊 أزرق المحيط",
                "description": "ثيم أزرق هادئ مستوحى من المحيط",
                "colors": self.get_ocean_blue_colors()
            },
            "forest_green": {
                "name": "🌲 أخضر الغابة",
                "description": "ثيم أخضر طبيعي مريح للعين",
                "colors": self.get_forest_green_colors()
            },
            "sunset_orange": {
                "name": "🌅 برتقالي الغروب",
                "description": "ثيم دافئ بألوان الغروب",
                "colors": self.get_sunset_orange_colors()
            }
        }
    
    def get_light_modern_colors(self) -> Dict[str, str]:
        """ألوان الثيم المودرن الفاتح"""
        return {
            # الألوان الأساسية
            'primary': '#6366F1',           # بنفسجي عصري
            'primary_dark': '#4F46E5',      # بنفسجي داكن
            'primary_light': '#A5B4FC',     # بنفسجي فاتح
            'secondary': '#10B981',         # أخضر زمردي
            'accent': '#F59E0B',            # برتقالي ذهبي
            
            # ألوان الخلفية
            'background': '#FFFFFF',        # أبيض نقي
            'surface': '#F8FAFC',           # رمادي فاتح جداً
            'card_bg': '#FFFFFF',           # أبيض للكروت
            'sidebar_bg': '#F1F5F9',        # رمادي فاتح للشريط الجانبي
            
            # ألوان النص
            'text_primary': '#1E293B',      # رمادي داكن
            'text_secondary': '#64748B',    # رمادي متوسط
            'text_muted': '#94A3B8',        # رمادي فاتح
            'text_on_primary': '#FFFFFF',   # أبيض على الأساسي
            
            # ألوان الحدود والفواصل
            'border_color': '#E2E8F0',      # رمادي فاتح
            'divider': '#F1F5F9',           # فاصل فاتح
            
            # ألوان الحالة
            'success': '#10B981',           # أخضر نجاح
            'warning': '#F59E0B',           # برتقالي تحذير
            'error': '#EF4444',             # أحمر خطأ
            'info': '#3B82F6',              # أزرق معلومات
            
            # ألوان التفاعل
            'hover': '#F1F5F9',             # لون التمرير
            'active': '#E2E8F0',            # لون النشط
            'focus': '#6366F1',             # لون التركيز
            'disabled': '#9CA3AF',          # لون المعطل
            
            # ألوان الجداول
            'table_header': '#F8FAFC',      # رأس الجدول
            'table_row_even': '#FFFFFF',    # صف زوجي
            'table_row_odd': '#F8FAFC',     # صف فردي
            'table_selected': '#EEF2FF',    # صف محدد
        }
    
    def get_dark_elegant_colors(self) -> Dict[str, str]:
        """ألوان الثيم الأنيق الداكن"""
        return {
            # الألوان الأساسية
            'primary': '#D4AF37',           # ذهبي أنيق
            'primary_dark': '#B8860B',      # ذهبي داكن
            'primary_light': '#F0E68C',     # ذهبي فاتح
            'secondary': '#40E0D0',         # تركوازي
            'accent': '#FF6B6B',            # أحمر مرجاني
            
            # ألوان الخلفية
            'background': '#0F172A',        # أزرق داكن جداً
            'surface': '#1E293B',           # أزرق داكن
            'card_bg': '#334155',           # رمادي أزرق
            'sidebar_bg': '#1E293B',        # أزرق داكن للشريط
            
            # ألوان النص
            'text_primary': '#F1F5F9',      # أبيض مائل للرمادي
            'text_secondary': '#CBD5E1',    # رمادي فاتح
            'text_muted': '#94A3B8',        # رمادي متوسط
            'text_on_primary': '#0F172A',   # داكن على الذهبي
            
            # ألوان الحدود والفواصل
            'border_color': '#475569',      # رمادي أزرق
            'divider': '#334155',           # فاصل داكن
            
            # ألوان الحالة
            'success': '#22C55E',           # أخضر نجاح
            'warning': '#F59E0B',           # برتقالي تحذير
            'error': '#EF4444',             # أحمر خطأ
            'info': '#3B82F6',              # أزرق معلومات
            
            # ألوان التفاعل
            'hover': '#475569',             # لون التمرير
            'active': '#64748B',            # لون النشط
            'focus': '#D4AF37',             # لون التركيز الذهبي
            'disabled': '#64748B',          # لون المعطل
            
            # ألوان الجداول
            'table_header': '#1E293B',      # رأس الجدول
            'table_row_even': '#334155',    # صف زوجي
            'table_row_odd': '#1E293B',     # صف فردي
            'table_selected': '#475569',    # صف محدد
        }
    
    def get_ocean_blue_colors(self) -> Dict[str, str]:
        """ألوان ثيم أزرق المحيط"""
        return {
            # الألوان الأساسية
            'primary': '#0EA5E9',           # أزرق سماوي
            'primary_dark': '#0284C7',      # أزرق داكن
            'primary_light': '#7DD3FC',     # أزرق فاتح
            'secondary': '#06B6D4',         # سيان
            'accent': '#F97316',            # برتقالي مرجاني
            
            # ألوان الخلفية
            'background': '#F0F9FF',        # أزرق فاتح جداً
            'surface': '#E0F2FE',           # أزرق فاتح
            'card_bg': '#FFFFFF',           # أبيض للكروت
            'sidebar_bg': '#E0F2FE',        # أزرق فاتح للشريط
            
            # ألوان النص
            'text_primary': '#0C4A6E',      # أزرق داكن
            'text_secondary': '#0369A1',    # أزرق متوسط
            'text_muted': '#0284C7',        # أزرق فاتح
            'text_on_primary': '#FFFFFF',   # أبيض على الأزرق
            
            # ألوان الحدود والفواصل
            'border_color': '#BAE6FD',      # أزرق فاتح
            'divider': '#E0F2FE',           # فاصل أزرق
            
            # ألوان الحالة
            'success': '#059669',           # أخضر بحري
            'warning': '#D97706',           # برتقالي
            'error': '#DC2626',             # أحمر
            'info': '#0EA5E9',              # أزرق معلومات
            
            # ألوان التفاعل
            'hover': '#E0F2FE',             # لون التمرير
            'active': '#BAE6FD',            # لون النشط
            'focus': '#0EA5E9',             # لون التركيز
            'disabled': '#94A3B8',          # لون المعطل
            
            # ألوان الجداول
            'table_header': '#E0F2FE',      # رأس الجدول
            'table_row_even': '#FFFFFF',    # صف زوجي
            'table_row_odd': '#F0F9FF',     # صف فردي
            'table_selected': '#BAE6FD',    # صف محدد
        }
    
    def get_forest_green_colors(self) -> Dict[str, str]:
        """ألوان ثيم أخضر الغابة"""
        return {
            # الألوان الأساسية
            'primary': '#059669',           # أخضر غابة
            'primary_dark': '#047857',      # أخضر داكن
            'primary_light': '#34D399',     # أخضر فاتح
            'secondary': '#0D9488',         # أخضر مائل للأزرق
            'accent': '#F59E0B',            # برتقالي ذهبي
            
            # ألوان الخلفية
            'background': '#F0FDF4',        # أخضر فاتح جداً
            'surface': '#DCFCE7',           # أخضر فاتح
            'card_bg': '#FFFFFF',           # أبيض للكروت
            'sidebar_bg': '#DCFCE7',        # أخضر فاتح للشريط
            
            # ألوان النص
            'text_primary': '#14532D',      # أخضر داكن جداً
            'text_secondary': '#166534',    # أخضر داكن
            'text_muted': '#22C55E',        # أخضر متوسط
            'text_on_primary': '#FFFFFF',   # أبيض على الأخضر
            
            # ألوان الحدود والفواصل
            'border_color': '#BBF7D0',      # أخضر فاتح
            'divider': '#DCFCE7',           # فاصل أخضر
            
            # ألوان الحالة
            'success': '#059669',           # أخضر نجاح
            'warning': '#D97706',           # برتقالي تحذير
            'error': '#DC2626',             # أحمر خطأ
            'info': '#0891B2',              # أزرق معلومات
            
            # ألوان التفاعل
            'hover': '#DCFCE7',             # لون التمرير
            'active': '#BBF7D0',            # لون النشط
            'focus': '#059669',             # لون التركيز
            'disabled': '#9CA3AF',          # لون المعطل
            
            # ألوان الجداول
            'table_header': '#DCFCE7',      # رأس الجدول
            'table_row_even': '#FFFFFF',    # صف زوجي
            'table_row_odd': '#F0FDF4',     # صف فردي
            'table_selected': '#BBF7D0',    # صف محدد
        }
    
    def get_sunset_orange_colors(self) -> Dict[str, str]:
        """ألوان ثيم برتقالي الغروب"""
        return {
            # الألوان الأساسية
            'primary': '#EA580C',           # برتقالي دافئ
            'primary_dark': '#C2410C',      # برتقالي داكن
            'primary_light': '#FB923C',     # برتقالي فاتح
            'secondary': '#DC2626',         # أحمر
            'accent': '#FBBF24',            # أصفر ذهبي
            
            # ألوان الخلفية
            'background': '#FFF7ED',        # برتقالي فاتح جداً
            'surface': '#FFEDD5',           # برتقالي فاتح
            'card_bg': '#FFFFFF',           # أبيض للكروت
            'sidebar_bg': '#FFEDD5',        # برتقالي فاتح للشريط
            
            # ألوان النص
            'text_primary': '#7C2D12',      # برتقالي داكن جداً
            'text_secondary': '#9A3412',    # برتقالي داكن
            'text_muted': '#C2410C',        # برتقالي متوسط
            'text_on_primary': '#FFFFFF',   # أبيض على البرتقالي
            
            # ألوان الحدود والفواصل
            'border_color': '#FED7AA',      # برتقالي فاتح
            'divider': '#FFEDD5',           # فاصل برتقالي
            
            # ألوان الحالة
            'success': '#059669',           # أخضر نجاح
            'warning': '#D97706',           # برتقالي تحذير
            'error': '#DC2626',             # أحمر خطأ
            'info': '#0891B2',              # أزرق معلومات
            
            # ألوان التفاعل
            'hover': '#FFEDD5',             # لون التمرير
            'active': '#FED7AA',            # لون النشط
            'focus': '#EA580C',             # لون التركيز
            'disabled': '#9CA3AF',          # لون المعطل
            
            # ألوان الجداول
            'table_header': '#FFEDD5',      # رأس الجدول
            'table_row_even': '#FFFFFF',    # صف زوجي
            'table_row_odd': '#FFF7ED',     # صف فردي
            'table_selected': '#FED7AA',    # صف محدد
        }
    
    def get_current_colors(self) -> Dict[str, str]:
        """الحصول على ألوان الثيم الحالي"""
        themes = self.get_available_themes()
        if self.current_theme in themes:
            return themes[self.current_theme]['colors']
        return self.get_light_modern_colors()
    
    def set_theme(self, theme_name: str):
        """تعيين ثيم جديد"""
        themes = self.get_available_themes()
        if theme_name in themes:
            self.current_theme = theme_name
            self.save_theme_preference()
            return True
        return False
    
    def get_current_theme_info(self) -> Dict[str, Any]:
        """الحصول على معلومات الثيم الحالي"""
        themes = self.get_available_themes()
        if self.current_theme in themes:
            return themes[self.current_theme]
        return themes['light_modern']
    
    def save_theme_preference(self):
        """حفظ تفضيل الثيم"""
        try:
            settings = {"current_theme": self.current_theme}
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"Error saving theme preference: {e}")
    
    def load_theme_preference(self):
        """تحميل تفضيل الثيم"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    self.current_theme = settings.get('current_theme', 'light_modern')
        except Exception as e:
            print(f"Error loading theme preference: {e}")
            self.current_theme = 'light_modern'

    def get_stylesheet(self, widget_type: str = "general") -> str:
        """الحصول على stylesheet للعنصر المحدد"""
        colors = self.get_current_colors()

        if widget_type == "general":
            return self._get_general_stylesheet(colors)
        elif widget_type == "sidebar":
            return self._get_sidebar_stylesheet(colors)
        elif widget_type == "table":
            return self._get_table_stylesheet(colors)
        elif widget_type == "form":
            return self._get_form_stylesheet(colors)
        elif widget_type == "button":
            return self._get_button_stylesheet(colors)
        elif widget_type == "card":
            return self._get_card_stylesheet(colors)
        else:
            return self._get_general_stylesheet(colors)

    def _get_general_stylesheet(self, colors: Dict[str, str]) -> str:
        """الـ stylesheet العام"""
        return f"""
            QMainWindow {{
                background-color: {colors['background']};
                color: {colors['text_primary']};
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            }}

            QWidget {{
                background-color: {colors['background']};
                color: {colors['text_primary']};
                font-size: 14px;
            }}

            QFrame {{
                background-color: {colors['surface']};
                border: 1px solid {colors['border_color']};
                border-radius: 8px;
            }}

            QLabel {{
                color: {colors['text_primary']};
                background-color: transparent;
                font-size: 14px;
            }}

            QMenuBar {{
                background-color: {colors['primary']};
                color: {colors['text_on_primary']};
                border: none;
                padding: 4px;
                font-weight: 500;
            }}

            QMenuBar::item {{
                background-color: transparent;
                padding: 8px 16px;
                border-radius: 6px;
                margin: 2px;
            }}

            QMenuBar::item:selected {{
                background-color: {colors['primary_dark']};
            }}

            QMenu {{
                background-color: {colors['card_bg']};
                color: {colors['text_primary']};
                border: 1px solid {colors['border_color']};
                border-radius: 8px;
                padding: 8px;
            }}

            QMenu::item {{
                padding: 8px 16px;
                border-radius: 6px;
                margin: 2px;
            }}

            QMenu::item:selected {{
                background-color: {colors['hover']};
                color: {colors['primary']};
            }}

            QScrollBar:vertical {{
                background-color: {colors['surface']};
                width: 12px;
                border-radius: 6px;
            }}

            QScrollBar::handle:vertical {{
                background-color: {colors['border_color']};
                border-radius: 6px;
                min-height: 20px;
            }}

            QScrollBar::handle:vertical:hover {{
                background-color: {colors['primary_light']};
            }}
        """

    def _get_button_stylesheet(self, colors: Dict[str, str]) -> str:
        """stylesheet للأزرار"""
        return f"""
            QPushButton {{
                background-color: {colors['primary']};
                color: {colors['text_on_primary']};
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 500;
                min-height: 20px;
            }}

            QPushButton:hover {{
                background-color: {colors['primary_dark']};
                transform: translateY(-1px);
            }}

            QPushButton:pressed {{
                background-color: {colors['primary_dark']};
                transform: translateY(0px);
            }}

            QPushButton:disabled {{
                background-color: {colors['disabled']};
                color: {colors['text_muted']};
            }}

            QPushButton.secondary {{
                background-color: {colors['surface']};
                color: {colors['text_primary']};
                border: 2px solid {colors['border_color']};
            }}

            QPushButton.secondary:hover {{
                background-color: {colors['hover']};
                border-color: {colors['primary']};
            }}

            QPushButton.success {{
                background-color: {colors['success']};
                color: white;
            }}

            QPushButton.warning {{
                background-color: {colors['warning']};
                color: white;
            }}

            QPushButton.error {{
                background-color: {colors['error']};
                color: white;
            }}
        """

    def _get_form_stylesheet(self, colors: Dict[str, str]) -> str:
        """stylesheet للنماذج"""
        return f"""
            QLineEdit, QTextEdit, QComboBox {{
                background-color: {colors['card_bg']};
                color: {colors['text_primary']};
                border: 2px solid {colors['border_color']};
                border-radius: 8px;
                padding: 12px;
                font-size: 14px;
            }}

            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {{
                border-color: {colors['primary']};
                background-color: {colors['background']};
            }}

            QComboBox::drop-down {{
                border: none;
                width: 30px;
            }}

            QComboBox::down-arrow {{
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid {colors['text_secondary']};
            }}

            QGroupBox {{
                font-size: 16px;
                font-weight: bold;
                color: {colors['primary']};
                border: 2px solid {colors['primary_light']};
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: {colors['surface']};
            }}

            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: {colors['background']};
                border-radius: 6px;
            }}
        """

    def _get_table_stylesheet(self, colors: Dict[str, str]) -> str:
        """stylesheet للجداول"""
        return f"""
            QTableWidget {{
                background-color: {colors['card_bg']};
                color: {colors['text_primary']};
                border: 1px solid {colors['border_color']};
                border-radius: 8px;
                gridline-color: {colors['divider']};
                font-size: 14px;
            }}

            QTableWidget::item {{
                padding: 12px;
                border-bottom: 1px solid {colors['divider']};
            }}

            QTableWidget::item:selected {{
                background-color: {colors['table_selected']};
                color: {colors['text_primary']};
            }}

            QHeaderView::section {{
                background-color: {colors['table_header']};
                color: {colors['text_primary']};
                padding: 12px;
                border: none;
                border-bottom: 2px solid {colors['primary']};
                font-weight: bold;
                font-size: 14px;
            }}

            QTableWidget::item:alternate {{
                background-color: {colors['table_row_odd']};
            }}
        """

    def _get_sidebar_stylesheet(self, colors: Dict[str, str]) -> str:
        """stylesheet للشريط الجانبي"""
        return f"""
            QFrame {{
                background-color: {colors['sidebar_bg']};
                border: none;
                border-right: 1px solid {colors['border_color']};
            }}

            QPushButton {{
                background-color: transparent;
                color: {colors['text_primary']};
                border: none;
                padding: 16px 20px;
                text-align: left;
                font-size: 14px;
                font-weight: 500;
                border-radius: 8px;
                margin: 2px 8px;
            }}

            QPushButton:hover {{
                background-color: {colors['hover']};
                color: {colors['primary']};
            }}

            QPushButton:checked {{
                background-color: {colors['primary']};
                color: {colors['text_on_primary']};
            }}
        """

    def _get_card_stylesheet(self, colors: Dict[str, str]) -> str:
        """stylesheet للكروت"""
        return f"""
            QFrame {{
                background-color: {colors['card_bg']};
                border: 1px solid {colors['border_color']};
                border-radius: 12px;
                padding: 20px;
            }}

            QFrame:hover {{
                border-color: {colors['primary_light']};
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }}
        """

    def get_theme_preview_style(self, theme_name: str) -> str:
        """الحصول على معاينة للثيم"""
        themes = self.get_available_themes()
        if theme_name not in themes:
            return ""

        colors = themes[theme_name]['colors']
        return f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {colors['primary']}, stop:1 {colors['primary_light']});
                border: 2px solid {colors['border_color']};
                border-radius: 8px;
                min-height: 60px;
            }}

            QLabel {{
                color: {colors['text_on_primary']};
                font-weight: bold;
                background-color: transparent;
            }}
        """

# إنشاء مثيل عام
modern_theme_manager = ModernThemeManager()
