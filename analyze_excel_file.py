#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحليل ملف Excel لفهم سبب مشكلة الاستيراد
"""

import pandas as pd
import os
import sys

def analyze_excel_file():
    """تحليل ملف Excel المشكل"""
    
    # مسار الملف
    excel_file = "../New Text Document (2).xlsx"
    
    if not os.path.exists(excel_file):
        print(f"❌ الملف غير موجود: {excel_file}")
        return
    
    try:
        print("🔍 تحليل ملف Excel...")
        print("=" * 50)
        
        # قراءة الملف
        print("📖 قراءة الملف...")
        
        # قراءة جميع الأوراق
        excel_data = pd.read_excel(excel_file, sheet_name=None, engine='openpyxl')
        
        print(f"📊 عدد الأوراق في الملف: {len(excel_data)}")
        print("\n📋 أسماء الأوراق:")
        for i, sheet_name in enumerate(excel_data.keys(), 1):
            print(f"  {i}. {sheet_name}")
        
        # تحليل كل ورقة
        for sheet_name, df in excel_data.items():
            print(f"\n" + "="*60)
            print(f"📄 تحليل الورقة: {sheet_name}")
            print("="*60)
            
            print(f"📊 عدد الصفوف: {len(df)}")
            print(f"📊 عدد الأعمدة: {len(df.columns)}")
            
            print("\n📋 أسماء الأعمدة:")
            for i, col in enumerate(df.columns, 1):
                print(f"  {i}. {col}")
            
            # عرض أول 5 صفوف
            print("\n📋 أول 5 صفوف:")
            print(df.head().to_string())
            
            # فحص الأكواد إذا كان هناك عمود كود
            code_columns = [col for col in df.columns if any(keyword in str(col).lower() for keyword in ['code', 'كود', 'رقم'])]
            
            if code_columns:
                print(f"\n🔍 تحليل أعمدة الأكواد: {code_columns}")
                for code_col in code_columns:
                    print(f"\n  📊 العمود: {code_col}")
                    
                    # إحصائيات الكود
                    total_codes = len(df[code_col])
                    non_null_codes = df[code_col].notna().sum()
                    null_codes = df[code_col].isna().sum()
                    empty_codes = (df[code_col] == '').sum()
                    
                    print(f"    - إجمالي الأكواد: {total_codes}")
                    print(f"    - أكواد غير فارغة: {non_null_codes}")
                    print(f"    - أكواد NULL: {null_codes}")
                    print(f"    - أكواد فارغة (''): {empty_codes}")
                    
                    # فحص التكرار
                    if non_null_codes > 0:
                        # تنظيف الأكواد
                        clean_codes = df[code_col].dropna().astype(str).str.strip()
                        clean_codes = clean_codes[clean_codes != '']
                        
                        if len(clean_codes) > 0:
                            unique_codes = clean_codes.nunique()
                            total_clean_codes = len(clean_codes)
                            duplicates = total_clean_codes - unique_codes
                            
                            print(f"    - أكواد فريدة: {unique_codes}")
                            print(f"    - أكواد مكررة: {duplicates}")
                            
                            if duplicates > 0:
                                print(f"    ⚠️ يوجد {duplicates} كود مكرر!")
                                
                                # عرض الأكواد المكررة
                                duplicate_codes = clean_codes[clean_codes.duplicated(keep=False)]
                                duplicate_counts = duplicate_codes.value_counts()
                                
                                print("    📋 الأكواد المكررة:")
                                for code, count in duplicate_counts.head(10).items():
                                    print(f"      - '{code}': {count} مرة")
                                
                                if len(duplicate_counts) > 10:
                                    print(f"      ... و {len(duplicate_counts) - 10} أكواد مكررة أخرى")
                            else:
                                print("    ✅ لا توجد أكواد مكررة")
                    
                    # عرض عينة من الأكواد
                    print(f"\n    📋 عينة من الأكواد:")
                    sample_codes = df[code_col].dropna().head(10)
                    for i, code in enumerate(sample_codes, 1):
                        print(f"      {i}. '{code}'")
            
            # فحص أعمدة الأسماء
            name_columns = [col for col in df.columns if any(keyword in str(col).lower() for keyword in ['name', 'اسم', 'منتج', 'صنف'])]
            
            if name_columns:
                print(f"\n🔍 تحليل أعمدة الأسماء: {name_columns}")
                for name_col in name_columns:
                    print(f"\n  📊 العمود: {name_col}")
                    
                    # إحصائيات الأسماء
                    total_names = len(df[name_col])
                    non_null_names = df[name_col].notna().sum()
                    null_names = df[name_col].isna().sum()
                    empty_names = (df[name_col] == '').sum()
                    
                    print(f"    - إجمالي الأسماء: {total_names}")
                    print(f"    - أسماء غير فارغة: {non_null_names}")
                    print(f"    - أسماء NULL: {null_names}")
                    print(f"    - أسماء فارغة (''): {empty_names}")
                    
                    # عرض عينة من الأسماء
                    print(f"\n    📋 عينة من الأسماء:")
                    sample_names = df[name_col].dropna().head(10)
                    for i, name in enumerate(sample_names, 1):
                        print(f"      {i}. '{name}'")
            
            # إذا كانت الورقة كبيرة، لا نعرض كل التفاصيل
            if len(df) > 100:
                print(f"\n⚠️ الورقة كبيرة ({len(df)} صف) - تم عرض عينة فقط")
        
        print(f"\n" + "="*60)
        print("✅ انتهى تحليل الملف")
        
    except Exception as e:
        print(f"❌ خطأ في قراءة الملف: {e}")
        print(f"نوع الخطأ: {type(e).__name__}")
        
        # محاولة قراءة الملف بطرق مختلفة
        try:
            print("\n🔄 محاولة قراءة الملف بـ xlrd...")
            excel_data = pd.read_excel(excel_file, sheet_name=None, engine='xlrd')
            print("✅ نجحت قراءة الملف بـ xlrd")
        except Exception as e2:
            print(f"❌ فشل xlrd أيضاً: {e2}")
            
        try:
            print("\n🔄 محاولة قراءة الملف كـ CSV...")
            csv_data = pd.read_csv(excel_file, encoding='utf-8')
            print("✅ نجحت قراءة الملف كـ CSV")
            print(f"📊 عدد الصفوف: {len(csv_data)}")
            print(f"📊 عدد الأعمدة: {len(csv_data.columns)}")
        except Exception as e3:
            print(f"❌ فشل CSV أيضاً: {e3}")

if __name__ == "__main__":
    analyze_excel_file()
