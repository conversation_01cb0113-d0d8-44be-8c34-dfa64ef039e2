#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حذف جميع المنتجات من قاعدة البيانات بأمان
"""

import sys
import os
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from datetime import datetime

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.models import Product

def delete_all_products():
    """حذف جميع المنتجات من قاعدة البيانات مع إنشاء نسخة احتياطية"""
    
    try:
        engine = create_engine('sqlite:///accounting.db')
        Session = sessionmaker(bind=engine)
        
        with Session() as session:
            # عد المنتجات قبل الحذف
            products_count = session.query(Product).count()
            print(f"📊 عدد المنتجات قبل الحذف: {products_count}")
            
            if products_count == 0:
                print("✅ لا توجد منتجات للحذف")
                return
            
            # إنشاء نسخة احتياطية من المنتجات
            print("💾 إنشاء نسخة احتياطية من المنتجات...")
            backup_filename = f"products_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            
            products = session.query(Product).all()
            with open(backup_filename, 'w', encoding='utf-8') as f:
                f.write("# نسخة احتياطية من المنتجات\n")
                f.write(f"# تاريخ الإنشاء: {datetime.now()}\n")
                f.write(f"# عدد المنتجات: {len(products)}\n\n")
                
                for product in products:
                    f.write(f"ID: {product.id}\n")
                    f.write(f"الاسم: {product.name}\n")
                    f.write(f"الكود: {product.code}\n")
                    f.write(f"سعر الشراء: {product.purchase_price}\n")
                    f.write(f"سعر البيع: {product.sale_price}\n")
                    f.write(f"الكمية: {product.quantity}\n")
                    f.write(f"الوحدة: {product.unit}\n")
                    f.write(f"الباركود: {product.barcode}\n")
                    f.write(f"الفئة: {product.category}\n")
                    f.write(f"الوصف: {product.description}\n")
                    f.write("-" * 50 + "\n")
            
            print(f"✅ تم إنشاء نسخة احتياطية: {backup_filename}")
            
            # حذف الباركودات المرتبطة أولاً
            print("🗑️ حذف الباركودات المرتبطة...")
            deleted_barcodes = session.execute(text("""
                DELETE FROM product_barcodes
            """)).rowcount
            print(f"✅ تم حذف {deleted_barcodes} باركود")
            
            # حذف جميع المنتجات
            print("🗑️ حذف جميع المنتجات...")
            deleted_products = session.execute(text("""
                DELETE FROM products
            """)).rowcount
            
            # إعادة تعيين العداد التلقائي
            session.execute(text("""
                DELETE FROM sqlite_sequence WHERE name='products'
            """))
            
            # حفظ التغييرات
            session.commit()
            
            print(f"✅ تم حذف {deleted_products} منتج بنجاح")
            
            # التحقق من النتيجة
            remaining_count = session.query(Product).count()
            print(f"📊 عدد المنتجات بعد الحذف: {remaining_count}")
            
            if remaining_count == 0:
                print("🎉 تم حذف جميع المنتجات بنجاح!")
                print(f"💾 النسخة الاحتياطية محفوظة في: {backup_filename}")
            else:
                print(f"⚠️ تبقى {remaining_count} منتج لم يتم حذفه")
                
    except Exception as e:
        print(f"❌ خطأ في حذف المنتجات: {e}")
        print(f"نوع الخطأ: {type(e).__name__}")

def main():
    """الدالة الرئيسية"""
    print("🗑️ حذف جميع المنتجات من قاعدة البيانات")
    print("=" * 50)
    
    # تأكيد من المستخدم
    print("⚠️ تحذير: سيتم حذف جميع المنتجات من قاعدة البيانات!")
    print("💾 سيتم إنشاء نسخة احتياطية قبل الحذف")
    
    # تنفيذ الحذف
    delete_all_products()
    
    print("\n" + "=" * 50)
    print("✅ انتهت عملية الحذف")

if __name__ == "__main__":
    main()
