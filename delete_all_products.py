#!/usr/bin/env python3
"""
حذف جميع المنتجات من قاعدة البيانات
"""

import sqlite3
import os
from datetime import datetime

def delete_all_products():
    """حذف جميع المنتجات من قاعدة البيانات"""
    
    db_path = 'accounting.db'
    
    if not os.path.exists(db_path):
        print("❌ ملف قاعدة البيانات غير موجود!")
        return
    
    print("🗑️ بدء حذف جميع المنتجات...")
    print("=" * 60)
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # عدد المنتجات قبل الحذف
        cursor.execute('SELECT COUNT(*) FROM products')
        count_before = cursor.fetchone()[0]
        print(f"📊 عدد المنتجات قبل الحذف: {count_before}")
        
        if count_before == 0:
            print("ℹ️ لا توجد منتجات للحذف")
            conn.close()
            return
        
        # تأكيد الحذف
        print("")
        print("⚠️ تحذير: سيتم حذف جميع المنتجات نهائياً!")
        print("⚠️ هذه العملية لا يمكن التراجع عنها!")
        print("")
        
        confirm = input("هل أنت متأكد من حذف جميع المنتجات؟ اكتب 'نعم' للتأكيد: ")
        
        if confirm.lower() not in ['نعم', 'yes', 'y']:
            print("❌ تم إلغاء العملية")
            conn.close()
            return
        
        print("")
        print("🗑️ جاري حذف جميع المنتجات...")
        
        # إنشاء نسخة احتياطية سريعة
        backup_filename = f"products_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sql"
        print(f"💾 إنشاء نسخة احتياطية: {backup_filename}")
        
        cursor.execute('SELECT * FROM products')
        products = cursor.fetchall()
        
        # حفظ النسخة الاحتياطية
        with open(backup_filename, 'w', encoding='utf-8') as backup_file:
            backup_file.write("-- نسخة احتياطية من المنتجات\n")
            backup_file.write(f"-- تاريخ الإنشاء: {datetime.now()}\n")
            backup_file.write(f"-- عدد المنتجات: {len(products)}\n\n")
            
            # الحصول على أسماء الأعمدة
            cursor.execute("PRAGMA table_info(products)")
            columns = [column[1] for column in cursor.fetchall()]
            
            for product in products:
                values = []
                for value in product:
                    if value is None:
                        values.append('NULL')
                    elif isinstance(value, str):
                        escaped_value = value.replace("'", "''")
                        values.append(f"'{escaped_value}'")
                    else:
                        values.append(str(value))
                
                backup_file.write(f"INSERT INTO products ({', '.join(columns)}) VALUES ({', '.join(values)});\n")
        
        print(f"✅ تم حفظ النسخة الاحتياطية في: {backup_filename}")
        
        # حذف جميع المنتجات
        cursor.execute('DELETE FROM products')
        
        # تأكيد الحذف
        cursor.execute('SELECT COUNT(*) FROM products')
        count_after = cursor.fetchone()[0]
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        print("")
        print("✅ تم حذف جميع المنتجات بنجاح!")
        print(f"📊 عدد المنتجات بعد الحذف: {count_after}")
        print(f"🗑️ تم حذف {count_before} منتج")
        print("")
        print("📋 ملخص العملية:")
        print(f"   ✅ تم حذف {count_before} منتج")
        print(f"   💾 تم حفظ نسخة احتياطية: {backup_filename}")
        print(f"   📊 قاعدة البيانات الآن فارغة من المنتجات")
        print("")
        print("🎯 الآن يمكنك:")
        print("   1️⃣ تشغيل البرنامج")
        print("   2️⃣ الذهاب للمخزون (ستجده فارغ)")
        print("   3️⃣ استيراد المنتجات من ملف الإكسل")
        print("   4️⃣ مراقبة عملية الإضافة من الصفر")
        
    except Exception as e:
        print(f"❌ خطأ أثناء حذف المنتجات: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    delete_all_products()
