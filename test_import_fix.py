#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة استيراد المنتجات من Excel
"""

import sys
import os
import pandas as pd
from datetime import datetime

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.models import Product, Base
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

def create_test_excel():
    """إنشاء ملف Excel تجريبي لاختبار الاستيراد"""
    
    # بيانات تجريبية مع أكواد مختلفة
    products_data = [
        {
            'Name': 'منتج تجريبي 1',
            'Code': 'TEST001',
            'Description': 'وصف المنتج الأول',
            'Category': 'فئة تجريبية',
            'Purchase_Price': 100.0,
            'Sale_Price': 150.0,
            'Quantity': 10,
            'Unit': 'قطعة',
            'Barcode': '1234567890123'
        },
        {
            'Name': 'منتج تجريبي 2',
            'Code': 'TEST002',
            'Description': 'وصف المنتج الثاني',
            'Category': 'فئة تجريبية',
            'Purchase_Price': 200.0,
            'Sale_Price': 300.0,
            'Quantity': 5,
            'Unit': 'قطعة',
            'Barcode': '1234567890124'
        },
        {
            'Name': 'منتج تجريبي 3',
            'Code': 'TEST003',
            'Description': 'وصف المنتج الثالث',
            'Category': 'فئة أخرى',
            'Purchase_Price': 50.0,
            'Sale_Price': 75.0,
            'Quantity': 20,
            'Unit': 'قطعة',
            'Barcode': '1234567890125'
        },
        {
            'Name': 'منتج بدون كود',
            'Code': '',  # كود فارغ
            'Description': 'منتج بدون كود',
            'Category': 'فئة تجريبية',
            'Purchase_Price': 30.0,
            'Sale_Price': 45.0,
            'Quantity': 15,
            'Unit': 'قطعة',
            'Barcode': '1234567890126'
        },
        {
            'Name': 'منتج آخر بدون كود',
            'Code': '',  # كود فارغ آخر
            'Description': 'منتج آخر بدون كود',
            'Category': 'فئة تجريبية',
            'Purchase_Price': 40.0,
            'Sale_Price': 60.0,
            'Quantity': 8,
            'Unit': 'قطعة',
            'Barcode': '1234567890127'
        }
    ]
    
    # إنشاء DataFrame
    df = pd.DataFrame(products_data)
    
    # حفظ في ملف Excel
    filename = f"test_products_import_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    df.to_excel(filename, index=False, engine='openpyxl')
    
    print(f"✅ تم إنشاء ملف Excel التجريبي: {filename}")
    print(f"📊 عدد المنتجات: {len(products_data)}")
    print("📋 المنتجات:")
    for i, product in enumerate(products_data, 1):
        code_display = product['Code'] if product['Code'] else '[فارغ]'
        print(f"  {i}. {product['Name']} - كود: {code_display}")
    
    return filename

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        engine = create_engine('sqlite:///accounting.db')
        Session = sessionmaker(bind=engine)
        
        with Session() as session:
            # عد المنتجات الموجودة
            count = session.query(Product).count()
            print(f"📊 عدد المنتجات الموجودة في قاعدة البيانات: {count}")
            
            # عرض آخر 5 منتجات
            recent_products = session.query(Product).order_by(Product.id.desc()).limit(5).all()
            if recent_products:
                print("📋 آخر 5 منتجات:")
                for product in recent_products:
                    code_display = product.code if product.code else '[فارغ]'
                    print(f"  - {product.name} (كود: {code_display})")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        return False

def check_duplicate_codes():
    """فحص الأكواد المكررة في قاعدة البيانات"""
    try:
        engine = create_engine('sqlite:///accounting.db')
        Session = sessionmaker(bind=engine)
        
        with Session() as session:
            # البحث عن الأكواد المكررة
            duplicates = session.execute(text("""
                SELECT code, COUNT(*) as count 
                FROM products 
                WHERE code IS NOT NULL AND code != ''
                GROUP BY code 
                HAVING COUNT(*) > 1
            """)).fetchall()
            
            if duplicates:
                print(f"⚠️ وجد {len(duplicates)} كود مكرر:")
                for code, count in duplicates:
                    print(f"  - الكود '{code}' مكرر {count} مرة")
            else:
                print("✅ لا توجد أكواد مكررة")
                
            # فحص المنتجات بدون أكواد
            no_code_count = session.execute(text("""
                SELECT COUNT(*) 
                FROM products 
                WHERE code IS NULL OR code = ''
            """)).scalar()
            
            print(f"📊 عدد المنتجات بدون أكواد: {no_code_count}")
            
    except Exception as e:
        print(f"❌ خطأ في فحص الأكواد المكررة: {e}")

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار إصلاح مشكلة استيراد المنتجات")
    print("=" * 50)
    
    # اختبار الاتصال بقاعدة البيانات
    print("\n1️⃣ اختبار الاتصال بقاعدة البيانات...")
    if not test_database_connection():
        print("❌ فشل في الاتصال بقاعدة البيانات")
        return
    
    # فحص الأكواد المكررة
    print("\n2️⃣ فحص الأكواد المكررة...")
    check_duplicate_codes()
    
    # إنشاء ملف Excel تجريبي
    print("\n3️⃣ إنشاء ملف Excel تجريبي...")
    excel_file = create_test_excel()
    
    print(f"\n✅ تم إنشاء ملف الاختبار: {excel_file}")
    print("\n📋 الخطوات التالية:")
    print("1. افتح التطبيق الرئيسي")
    print("2. اذهب إلى أدوات → استيراد من EasAcc")
    print("3. اختر ملف Excel واختر الملف المنشأ")
    print(f"4. اختر الملف: {excel_file}")
    print("5. اضغط على 'استيراد البيانات المحددة'")
    print("6. راقب الرسائل في نافذة السجل")

if __name__ == "__main__":
    main()
