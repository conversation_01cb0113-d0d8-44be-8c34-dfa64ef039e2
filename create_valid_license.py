#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ترخيص صالح للاختبار
"""

import sys
import os
from datetime import datetime, timedelta

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_valid_license():
    """إنشاء ترخيص صالح للجهاز الحالي"""
    print("🔑 إنشاء ترخيص صالح للاختبار")
    print("=" * 40)
    
    try:
        from license_manager import LicenseManager
        
        # إنشاء مدير التراخيص
        lm = LicenseManager()
        
        # عرض معلومات الجهاز
        customer_code = lm.get_customer_code()
        machine_id = lm.get_machine_id()
        
        print(f"🔑 كود العميل: {customer_code}")
        print(f"💻 رقم الجهاز: {machine_id}")
        
        # إنشاء ترخيص صالح لمدة 365 يوم
        expiry_date = datetime.now() + timedelta(days=365)
        
        valid_license = {
            "customer_code": customer_code,
            "machine_id": machine_id,
            "expiry_date": expiry_date.isoformat(),
            "license_type": "FULL",
            "created_date": datetime.now().isoformat()
        }
        
        # حفظ الترخيص
        if lm._save_license(valid_license):
            print("✅ تم إنشاء ترخيص صالح بنجاح!")
            print(f"📅 صالح حتى: {expiry_date.strftime('%d/%m/%Y')}")
            
            # فحص الترخيص للتأكد
            status = lm.check_license()
            
            print(f"\n📊 نتيجة الفحص:")
            print(f"   - صالح: {status['valid']}")
            print(f"   - الحالة: {status['status']}")
            print(f"   - الرسالة: {status['message']}")
            
            if status["valid"]:
                print(f"   - ينتهي في: {status['expiry_date'].strftime('%d/%m/%Y')}")
                print(f"   - متبقي: {status['days_remaining']} يوم")
                print("\n🎉 الترخيص صالح - يمكن تشغيل البرنامج الآن!")
                return True
            else:
                print("❌ خطأ: الترخيص المنشأ غير صالح!")
                return False
        else:
            print("❌ فشل في حفظ الترخيص")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء الترخيص: {e}")
        return False

def test_license_after_creation():
    """اختبار الترخيص بعد الإنشاء"""
    print("\n🧪 اختبار الترخيص بعد الإنشاء...")
    
    try:
        from license_ui import check_license_and_show_dialog
        
        # محاكاة فحص الترخيص (بدون عرض النافذة)
        from license_manager import LicenseManager
        lm = LicenseManager()
        status = lm.check_license()
        
        if status["valid"]:
            print("✅ check_license_and_show_dialog سيرجع True")
            print("✅ البرنامج سيعمل بدون عرض نافذة التفعيل")
            return True
        else:
            print("❌ check_license_and_show_dialog سيرجع False")
            print("❌ البرنامج سيعرض نافذة التفعيل")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الترخيص: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔐 إنشاء ترخيص صالح للاختبار")
    print("=" * 50)
    
    # إنشاء ترخيص صالح
    if create_valid_license():
        print("\n" + "="*50)
        
        # اختبار الترخيص
        if test_license_after_creation():
            print("\n🎉 تم إنشاء ترخيص صالح بنجاح!")
            print("🚀 يمكنك الآن تشغيل البرنامج الرئيسي:")
            print("   python main.py")
        else:
            print("\n❌ فشل في اختبار الترخيص")
    else:
        print("\n❌ فشل في إنشاء الترخيص")

if __name__ == "__main__":
    main()
