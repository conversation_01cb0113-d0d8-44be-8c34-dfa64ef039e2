// qabstractaxis.sip generated by MetaSIP
//
// This file is part of the QtChart Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtChart.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


namespace QtCharts
{
%TypeHeaderCode
#include <qabstractaxis.h>
%End

    class QAbstractAxis : public QObject /NoDefaultCtors/
    {
%TypeHeaderCode
#include <qabstractaxis.h>
%End

    public:
        enum AxisType
        {
            AxisTypeNoAxis,
%If (- QtChart_1_1_0)
            AxisTypeValues,
%End
%If (- QtChart_1_1_0)
            AxisTypeCategories,
%End
%If (QtChart_1_1_0 -)
            AxisTypeValue,
%End
%If (QtChart_1_1_0 -)
            AxisTypeBarCategory,
%End
%If (QtChart_1_1_0 -)
            AxisTypeCategory,
%End
%If (QtChart_1_1_0 -)
            AxisTypeDateTime,
%End
%If (QtChart_1_2_0 -)
            AxisTypeLogValue,
%End
        };

        typedef QFlags<QtCharts::QAbstractAxis::AxisType> AxisTypes;
        virtual ~QAbstractAxis();
        virtual QtCharts::QAbstractAxis::AxisType type() const = 0;
        bool isVisible() const;
        void setVisible(bool visible = true);
%If (QtChart_1_1_0 -)
        bool isLineVisible() const;
%End
%If (- QtChart_1_1_0)
        bool isArrowVisible() const;
%End
%If (QtChart_1_1_0 -)
        void setLineVisible(bool visible = true);
%End
%If (- QtChart_1_1_0)
        void setArrowVisible(bool visible = true);
%End
%If (QtChart_1_1_0 -)
        void setLinePen(const QPen &pen);
%End
%If (- QtChart_1_1_0)
        void setAxisPen(const QPen &pen);
%End
%If (QtChart_1_1_0 -)
        QPen linePen() const;
%End
%If (- QtChart_1_1_0)
        QPen axisPen() const;
%End
%If (QtChart_1_1_0 -)
        void setLinePenColor(QColor color);
%End
%If (- QtChart_1_1_0)
        void setAxisPenColor(QColor color);
%End
%If (QtChart_1_1_0 -)
        QColor linePenColor() const;
%End
%If (- QtChart_1_1_0)
        QColor axisPenColor() const;
%End
        bool isGridLineVisible() const;
        void setGridLineVisible(bool visible = true);
        void setGridLinePen(const QPen &pen);
        QPen gridLinePen() const;
        bool labelsVisible() const;
        void setLabelsVisible(bool visible = true);
%If (- QtChart_2_0_0)
        void setLabelsPen(const QPen &pen);
%End
%If (- QtChart_2_0_0)
        QPen labelsPen() const;
%End
        void setLabelsBrush(const QBrush &brush);
        QBrush labelsBrush() const;
        void setLabelsFont(const QFont &font);
        QFont labelsFont() const;
        void setLabelsAngle(int angle);
        int labelsAngle() const;
        void setLabelsColor(QColor color);
        QColor labelsColor() const;
        bool shadesVisible() const;
        void setShadesVisible(bool visible = true);
        void setShadesPen(const QPen &pen);
        QPen shadesPen() const;
        void setShadesBrush(const QBrush &brush);
        QBrush shadesBrush() const;
        void setShadesColor(QColor color);
        QColor shadesColor() const;
        void setShadesBorderColor(QColor color);
        QColor shadesBorderColor() const;
        void setMin(const QVariant &min);
        void setMax(const QVariant &max);
        void setRange(const QVariant &min, const QVariant &max);
        void show();
        void hide();
%If (QtChart_2_0_0 -)
        Qt::Orientation orientation() const;
%End
%If (QtChart_1_1_0 - QtChart_2_0_0)
        Qt::Orientation orientation();
%End

    signals:
        void visibleChanged(bool visible);
%If (QtChart_1_1_0 -)
        void lineVisibleChanged(bool visible);
%End
%If (- QtChart_1_1_0)
        void arrowVisibleChanged(bool visible);
%End
        void labelsVisibleChanged(bool visible);
        void gridVisibleChanged(bool visible);
        void colorChanged(QColor color);
        void labelsColorChanged(QColor color);
        void shadesVisibleChanged(bool visible);
        void shadesColorChanged(QColor color);
        void shadesBorderColorChanged(QColor color);

    public:
%If (QtChart_1_2_0 -)
        bool isTitleVisible() const;
%End
%If (QtChart_1_2_0 -)
        void setTitleVisible(bool visible = true);
%End
%If (QtChart_1_2_0 - QtChart_2_0_0)
        void setTitlePen(const QPen &pen);
%End
%If (QtChart_1_2_0 - QtChart_2_0_0)
        QPen titlePen() const;
%End
%If (QtChart_1_2_0 -)
        void setTitleBrush(const QBrush &brush);
%End
%If (QtChart_1_2_0 -)
        QBrush titleBrush() const;
%End
%If (QtChart_1_2_0 -)
        void setTitleFont(const QFont &font);
%End
%If (QtChart_1_2_0 -)
        QFont titleFont() const;
%End
%If (QtChart_1_2_0 -)
        void setTitleText(const QString &title);
%End
%If (QtChart_1_2_0 -)
        QString titleText() const;
%End
%If (QtChart_1_2_0 -)
        Qt::Alignment alignment() const;
%End

    signals:
%If (QtChart_1_2_0 -)
        void linePenChanged(const QPen &pen);
%End
%If (QtChart_1_2_0 - QtChart_2_0_0)
        void labelsPenChanged(const QPen &pen);
%End
%If (QtChart_1_2_0 -)
        void labelsBrushChanged(const QBrush &brush);
%End
%If (QtChart_1_2_0 -)
        void labelsFontChanged(const QFont &pen);
%End
%If (QtChart_1_2_0 -)
        void labelsAngleChanged(int angle);
%End
%If (QtChart_1_2_0 -)
        void gridLinePenChanged(const QPen &pen);
%End
%If (QtChart_1_2_0 -)
        void titleTextChanged(const QString &title);
%End
%If (QtChart_1_2_0 - QtChart_2_0_0)
        void titlePenChanged(const QPen &pen);
%End
%If (QtChart_1_2_0 -)
        void titleBrushChanged(const QBrush &brush);
%End
%If (QtChart_1_2_0 -)
        void titleVisibleChanged(bool visible);
%End
%If (QtChart_1_2_0 -)
        void titleFontChanged(const QFont &font);
%End
%If (QtChart_1_2_0 -)
        void shadesPenChanged(const QPen &pen);
%End
%If (QtChart_1_2_0 -)
        void shadesBrushChanged(const QBrush &brush);
%End

    public:
%If (QtChart_2_1_0 -)
        bool isMinorGridLineVisible() const;
%End
%If (QtChart_2_1_0 -)
        void setMinorGridLineVisible(bool visible = true);
%End
%If (QtChart_2_1_0 -)
        void setMinorGridLinePen(const QPen &pen);
%End
%If (QtChart_2_1_0 -)
        QPen minorGridLinePen() const;
%End
%If (QtChart_2_1_0 -)
        void setGridLineColor(const QColor &color);
%End
%If (QtChart_2_1_0 -)
        QColor gridLineColor();
%End
%If (QtChart_2_1_0 -)
        void setMinorGridLineColor(const QColor &color);
%End
%If (QtChart_2_1_0 -)
        QColor minorGridLineColor();
%End
%If (QtChart_2_1_0 -)
        void setReverse(bool reverse = true);
%End
%If (QtChart_2_1_0 -)
        bool isReverse() const;
%End

    signals:
%If (QtChart_2_1_0 -)
        void minorGridVisibleChanged(bool visible);
%End
%If (QtChart_2_1_0 -)
        void minorGridLinePenChanged(const QPen &pen);
%End
%If (QtChart_2_1_0 -)
        void gridLineColorChanged(const QColor &color);
%End
%If (QtChart_2_1_0 -)
        void minorGridLineColorChanged(const QColor &color);
%End
%If (QtChart_2_1_0 -)
        void reverseChanged(bool reverse);
%End

    public:
%If (QtChart_5_13_0 -)
        void setLabelsEditable(bool editable = true);
%End
%If (QtChart_5_13_0 -)
        bool labelsEditable() const;
%End

    signals:
%If (QtChart_5_13_0 -)
        void labelsEditableChanged(bool editable);
%End
    };
};
