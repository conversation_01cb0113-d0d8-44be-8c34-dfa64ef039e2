# ملف المخزون الجديد - تم إنشاؤه من الصفر
# تاريخ الإنشاء: 2025-06-03
# المميزات: عمود المكسب، تبويب إدارة الأصناف، التحديث التلقائي للقيم

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QPushButton, QLineEdit, QLabel, QSpinBox,
                             QDoubleSpinBox, QTableWidgetItem, QMessageBox,
                             QFrame, QGridLayout, QHeaderView, QComboBox,
                             QTabWidget, QDialog, QFormLayout, QTextEdit, QShortcut, QScrollArea)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QIcon, QColor, QKeySequence
from sqlalchemy.orm import Session
from sqlalchemy import or_
from database.models import Product, ProductBarcode
from utils.dialog_utils import setup_large_dialog, setup_medium_dialog
from utils.currency_formatter import format_currency, format_number
from gui.modern_theme import COLORS, FONTS, SPACING, RADIUS, get_modern_table_style
from utils.theme_manager import theme_manager

# قائمة الوحدات المتاحة
UNITS = ["قطعة", "كيلوجرام", "متر", "لتر", "طن", "علبة", "كرتون"]

class ProductDialog(QDialog):
    """نافذة إضافة/تعديل المنتج مع التحديث التلقائي للقيم"""
    
    def __init__(self, engine, product=None, parent=None):
        super().__init__(parent)
        self.engine = engine
        self.product = product
        self.saved_product_id = None  # لحفظ ID المنتج الجديد
        # إعداد النافذة مع خاصية التكبير
        title = "إضافة منتج جديد" if not product else "تعديل المنتج"
        setup_large_dialog(self, title, 1000, 1200, 1200, 1400)

        self.setup_ui()
        if product:
            self.load_product_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إنشاء scroll area للتعامل مع الشاشات الصغيرة
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # إنشاء widget رئيسي للمحتوى
        main_widget = QWidget()
        layout = QVBoxLayout()
        main_widget.setLayout(layout)

        # إضافة المحتوى للـ scroll area
        scroll_area.setWidget(main_widget)

        # تعيين scroll area كـ layout رئيسي
        main_layout = QVBoxLayout()
        main_layout.addWidget(scroll_area)
        self.setLayout(main_layout)
        
        # عنوان النافذة
        title = QLabel("إضافة منتج جديد" if not self.product else "تعديل المنتج")
        title.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2C3E50;
                padding: 20px;
                text-align: center;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # إطار الحقول
        fields_frame = QFrame()
        fields_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border-radius: 10px;
                padding: 20px;
                margin: 10px;
            }
        """)
        fields_layout = QGridLayout()
        fields_frame.setLayout(fields_layout)
        
        # حقول البيانات الأساسية
        fields_layout.addWidget(QLabel("اسم المنتج:"), 0, 0)
        self.name_input = QLineEdit()
        self.name_input.setStyleSheet("padding: 8px; font-size: 14px;")
        fields_layout.addWidget(self.name_input, 0, 1, 1, 3)
        
        fields_layout.addWidget(QLabel("كود المنتج:"), 1, 0)
        self.code_input = QLineEdit()
        self.code_input.setStyleSheet("padding: 8px; font-size: 14px;")
        fields_layout.addWidget(self.code_input, 1, 1)
        
        fields_layout.addWidget(QLabel("الفئة:"), 1, 2)
        self.category_input = QLineEdit()
        self.category_input.setStyleSheet("padding: 8px; font-size: 14px;")
        fields_layout.addWidget(self.category_input, 1, 3)
        
        fields_layout.addWidget(QLabel("الوحدة:"), 2, 0)
        self.unit_combo = QComboBox()
        self.unit_combo.addItems(UNITS)
        self.unit_combo.setStyleSheet("padding: 8px; font-size: 14px;")
        fields_layout.addWidget(self.unit_combo, 2, 1)
        
        # قسم الباركودات المتعددة
        barcode_section = QFrame()
        barcode_section.setStyleSheet("""
            QFrame {
                background-color: #F0F8FF;
                border: 2px solid #3498DB;
                border-radius: 8px;
                padding: 15px;
                margin: 10px 0;
            }
        """)
        barcode_layout = QGridLayout()
        barcode_section.setLayout(barcode_layout)

        barcode_title = QLabel("📊 الباركودات المتعددة")
        barcode_title.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2C3E50;
            margin-bottom: 10px;
            background: none;
            border: none;
        """)
        barcode_layout.addWidget(barcode_title, 0, 0, 1, 4)

        # الباركود الأساسي
        primary_label = QLabel("🔵 الباركود الأساسي:")
        primary_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2C3E50; min-width: 200px; padding: 5px;")
        barcode_layout.addWidget(primary_label, 1, 0)

        self.barcode_input = QLineEdit()
        self.barcode_input.setStyleSheet("""
            padding: 12px;
            font-size: 14px;
            border: 2px solid #3498DB;
            border-radius: 5px;
            background-color: white;
            min-height: 20px;
            min-width: 300px;
        """)
        self.barcode_input.setPlaceholderText("الباركود الرئيسي للمنتج")
        barcode_layout.addWidget(self.barcode_input, 1, 1)

        # أزرار مسح الباركود للحقل الأساسي
        primary_camera_btn = QPushButton("📷")
        primary_camera_btn.setFixedSize(45, 45)
        primary_camera_btn.setStyleSheet("""
            QPushButton {
                background-color: #17A2B8;
                color: white;
                font-size: 18px;
                border: none;
                border-radius: 8px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        primary_camera_btn.setToolTip("مسح الباركود بالكاميرا")
        primary_camera_btn.clicked.connect(lambda: self.scan_barcode_for_field(self.barcode_input, "camera"))
        barcode_layout.addWidget(primary_camera_btn, 1, 2)

        primary_device_btn = QPushButton("🔍")
        primary_device_btn.setFixedSize(45, 45)
        primary_device_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                color: white;
                font-size: 18px;
                border: none;
                border-radius: 8px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        primary_device_btn.setToolTip("مسح الباركود بالجهاز")
        primary_device_btn.clicked.connect(lambda: self.scan_barcode_for_field(self.barcode_input, "device"))
        barcode_layout.addWidget(primary_device_btn, 1, 3)

        # الباركودات الإضافية
        additional1_label = QLabel("🔸 باركود إضافي 1:")
        additional1_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2C3E50; min-width: 200px; padding: 5px;")
        barcode_layout.addWidget(additional1_label, 2, 0)

        self.barcode_2_input = QLineEdit()
        self.barcode_2_input.setStyleSheet("""
            padding: 12px;
            font-size: 14px;
            border: 1px solid #BDC3C7;
            border-radius: 5px;
            background-color: white;
            min-height: 20px;
            min-width: 300px;
        """)
        self.barcode_2_input.setPlaceholderText("باركود من مورد آخر")
        barcode_layout.addWidget(self.barcode_2_input, 2, 1)

        # أزرار مسح الباركود للحقل الإضافي 1
        add1_camera_btn = QPushButton("📷")
        add1_camera_btn.setFixedSize(45, 45)
        add1_camera_btn.setStyleSheet("""
            QPushButton {
                background-color: #17A2B8;
                color: white;
                font-size: 18px;
                border: none;
                border-radius: 8px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        add1_camera_btn.setToolTip("مسح الباركود بالكاميرا")
        add1_camera_btn.clicked.connect(lambda: self.scan_barcode_for_field(self.barcode_2_input, "camera"))
        barcode_layout.addWidget(add1_camera_btn, 2, 2)

        add1_device_btn = QPushButton("🔍")
        add1_device_btn.setFixedSize(45, 45)
        add1_device_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                color: white;
                font-size: 18px;
                border: none;
                border-radius: 8px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        add1_device_btn.setToolTip("مسح الباركود بالجهاز")
        add1_device_btn.clicked.connect(lambda: self.scan_barcode_for_field(self.barcode_2_input, "device"))
        barcode_layout.addWidget(add1_device_btn, 2, 3)

        additional2_label = QLabel("🔸 باركود إضافي 2:")
        additional2_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2C3E50; min-width: 200px; padding: 5px;")
        barcode_layout.addWidget(additional2_label, 3, 0)

        self.barcode_3_input = QLineEdit()
        self.barcode_3_input.setStyleSheet("""
            padding: 12px;
            font-size: 14px;
            border: 1px solid #BDC3C7;
            border-radius: 5px;
            background-color: white;
            min-height: 20px;
            min-width: 300px;
        """)
        self.barcode_3_input.setPlaceholderText("باركود حجم مختلف")
        barcode_layout.addWidget(self.barcode_3_input, 3, 1)

        # أزرار مسح الباركود للحقل الإضافي 2
        add2_camera_btn = QPushButton("📷")
        add2_camera_btn.setFixedSize(45, 45)
        add2_camera_btn.setStyleSheet("""
            QPushButton {
                background-color: #17A2B8;
                color: white;
                font-size: 18px;
                border: none;
                border-radius: 8px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        add2_camera_btn.setToolTip("مسح الباركود بالكاميرا")
        add2_camera_btn.clicked.connect(lambda: self.scan_barcode_for_field(self.barcode_3_input, "camera"))
        barcode_layout.addWidget(add2_camera_btn, 3, 2)

        add2_device_btn = QPushButton("🔍")
        add2_device_btn.setFixedSize(45, 45)
        add2_device_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                color: white;
                font-size: 18px;
                border: none;
                border-radius: 8px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        add2_device_btn.setToolTip("مسح الباركود بالجهاز")
        add2_device_btn.clicked.connect(lambda: self.scan_barcode_for_field(self.barcode_3_input, "device"))
        barcode_layout.addWidget(add2_device_btn, 3, 3)

        additional3_label = QLabel("🔸 باركود إضافي 3:")
        additional3_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2C3E50; min-width: 200px; padding: 5px;")
        barcode_layout.addWidget(additional3_label, 4, 0)

        self.barcode_4_input = QLineEdit()
        self.barcode_4_input.setStyleSheet("""
            padding: 12px;
            font-size: 14px;
            border: 1px solid #BDC3C7;
            border-radius: 5px;
            background-color: white;
            min-height: 20px;
            min-width: 300px;
        """)
        self.barcode_4_input.setPlaceholderText("باركود داخلي")
        barcode_layout.addWidget(self.barcode_4_input, 4, 1)

        # أزرار مسح الباركود للحقل الإضافي 3
        add3_camera_btn = QPushButton("📷")
        add3_camera_btn.setFixedSize(45, 45)
        add3_camera_btn.setStyleSheet("""
            QPushButton {
                background-color: #17A2B8;
                color: white;
                font-size: 18px;
                border: none;
                border-radius: 8px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        add3_camera_btn.setToolTip("مسح الباركود بالكاميرا")
        add3_camera_btn.clicked.connect(lambda: self.scan_barcode_for_field(self.barcode_4_input, "camera"))
        barcode_layout.addWidget(add3_camera_btn, 4, 2)

        add3_device_btn = QPushButton("🔍")
        add3_device_btn.setFixedSize(45, 45)
        add3_device_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                color: white;
                font-size: 18px;
                border: none;
                border-radius: 8px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        add3_device_btn.setToolTip("مسح الباركود بالجهاز")
        add3_device_btn.clicked.connect(lambda: self.scan_barcode_for_field(self.barcode_4_input, "device"))
        barcode_layout.addWidget(add3_device_btn, 4, 3)

        fields_layout.addWidget(barcode_section, 2, 0, 1, 4)
        
        # حقول الأسعار والكمية
        fields_layout.addWidget(QLabel("الكمية:"), 3, 0)
        self.quantity = QSpinBox()
        self.quantity.setRange(0, 999999)
        self.quantity.setStyleSheet("padding: 8px; font-size: 14px;")
        fields_layout.addWidget(self.quantity, 3, 1)

        fields_layout.addWidget(QLabel("الحد الأدنى للمخزون:"), 3, 2)
        self.min_quantity = QSpinBox()
        self.min_quantity.setRange(0, 999999)
        self.min_quantity.setStyleSheet("padding: 8px; font-size: 14px;")
        self.min_quantity.setToolTip("الكمية التي عندها يظهر تنبيه نفاد المخزون")
        fields_layout.addWidget(self.min_quantity, 3, 3)

        fields_layout.addWidget(QLabel("سعر الشراء:"), 4, 0)
        self.purchase_price = QDoubleSpinBox()
        self.purchase_price.setRange(0, 999999)
        self.purchase_price.setDecimals(0)  # إزالة الخانات العشرية
        self.purchase_price.setStyleSheet("padding: 8px; font-size: 14px;")
        fields_layout.addWidget(self.purchase_price, 4, 1)

        fields_layout.addWidget(QLabel("سعر البيع:"), 4, 2)
        self.sale_price = QDoubleSpinBox()
        self.sale_price.setRange(0, 999999)
        self.sale_price.setDecimals(0)  # إزالة الخانات العشرية
        self.sale_price.setStyleSheet("padding: 8px; font-size: 14px;")
        fields_layout.addWidget(self.sale_price, 4, 3)

        # إضافة حقل الحد الأدنى للبيع
        fields_layout.addWidget(QLabel("الحد الأدنى للبيع:"), 5, 0)
        self.min_sale_price = QDoubleSpinBox()
        self.min_sale_price.setRange(0, 999999)
        self.min_sale_price.setDecimals(0)
        self.min_sale_price.setStyleSheet("padding: 8px; font-size: 14px;")
        self.min_sale_price.setToolTip("أقل سعر مسموح للبيع - سيظهر تحذير عند البيع بأقل من هذا السعر")
        fields_layout.addWidget(self.min_sale_price, 5, 1)
        
        # حقول القيم المحسوبة
        total_value_label = QLabel("القيمة الإجمالية:")
        total_value_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2C3E50;")
        fields_layout.addWidget(total_value_label, 6, 0)

        total_desc_label = QLabel("(الكمية × سعر الشراء)")
        total_desc_label.setStyleSheet("font-size: 12px; color: #7F8C8D;")
        fields_layout.addWidget(total_desc_label, 6, 1)

        self.total_value_label = QLabel("0 ج.م")
        self.total_value_label.setStyleSheet("""
            QLabel {
                background-color: #E3F2FD;
                border: 2px solid #2196F3;
                border-radius: 5px;
                padding: 8px;
                font-size: 16px;
                font-weight: bold;
                color: #1976D2;
                min-width: 120px;
            }
        """)
        fields_layout.addWidget(self.total_value_label, 6, 2, 1, 2)

        profit_label = QLabel("المكسب المتوقع:")
        profit_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2C3E50;")
        fields_layout.addWidget(profit_label, 7, 0)

        profit_desc_label = QLabel("(الكمية × سعر البيع)")
        profit_desc_label.setStyleSheet("font-size: 12px; color: #7F8C8D;")
        fields_layout.addWidget(profit_desc_label, 7, 1)

        self.profit_value_label = QLabel("0 ج.م")
        self.profit_value_label.setStyleSheet("""
            QLabel {
                background-color: #E8F5E8;
                border: 2px solid #4CAF50;
                border-radius: 5px;
                padding: 8px;
                font-size: 16px;
                font-weight: bold;
                color: #388E3C;
                min-width: 120px;
            }
        """)
        fields_layout.addWidget(self.profit_value_label, 7, 2, 1, 2)

        # حقل الوصف
        fields_layout.addWidget(QLabel("الوصف:"), 8, 0)
        self.description_input = QTextEdit()
        self.description_input.setMaximumHeight(80)
        self.description_input.setStyleSheet("padding: 8px; font-size: 14px;")
        fields_layout.addWidget(self.description_input, 8, 1, 1, 3)
        
        layout.addWidget(fields_frame)
        
        # أزرار التحكم
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout()
        buttons_frame.setLayout(buttons_layout)
        
        self.save_btn = QPushButton("💾 حفظ")
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 12px 30px;
                border: none;
                border-radius: 8px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        self.save_btn.clicked.connect(self.save_product)
        
        self.cancel_btn = QPushButton("❌ إلغاء")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #DC3545;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 12px 30px;
                border: none;
                border-radius: 8px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #C82333;
            }
        """)
        self.cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.cancel_btn)
        buttons_layout.addStretch()
        
        layout.addWidget(buttons_frame)
        
        # ربط التغييرات بالتحديث التلقائي (في نهاية setup_ui)
        self.quantity.valueChanged.connect(self.update_calculated_values)
        self.purchase_price.valueChanged.connect(self.update_calculated_values)
        self.sale_price.valueChanged.connect(self.update_calculated_values)
        
        # تحديث القيم الأولية
        self.update_calculated_values()

    def scan_barcode_for_field(self, field, scan_type):
        """مسح الباركود وإدخاله في الحقل المحدد"""
        try:
            if scan_type == "camera":
                # استيراد نافذة مسح الباركود بالكاميرا
                from gui.barcode_scanner import BarcodeScannerDialog
                scanner = BarcodeScannerDialog(self)
                scanner.setWindowTitle("📷 مسح الباركود بالكاميرا")

            elif scan_type == "device":
                # استيراد نافذة مسح الباركود بالجهاز المخصص
                from gui.device_barcode_scanner import DeviceBarcodeScannerDialog
                scanner = DeviceBarcodeScannerDialog(self)

            else:
                QMessageBox.warning(self, "خطأ", "نوع المسح غير صحيح")
                return

            # فتح النافذة والحصول على النتيجة
            if scanner.exec_() == QDialog.Accepted and hasattr(scanner, 'scanned_code') and scanner.scanned_code:
                field.setText(scanner.scanned_code)
                QMessageBox.information(self, "نجح المسح ✅",
                                      f"تم مسح الباركود بنجاح:\n\n🔍 {scanner.scanned_code}\n\n✅ تم إدخال الباركود في الحقل")

        except ImportError as e:
            QMessageBox.warning(self, "خطأ في الاستيراد",
                              f"نافذة مسح الباركود غير متوفرة:\n{str(e)}\n\nيرجى التأكد من تثبيت المكتبات المطلوبة.")
        except Exception as e:
            QMessageBox.critical(self, "خطأ في المسح",
                               f"حدث خطأ أثناء مسح الباركود:\n\n❌ {str(e)}\n\nيرجى المحاولة مرة أخرى.")

    def update_calculated_values(self):
        """تحديث القيم المحسوبة تلقائياً عند تغيير الكمية أو الأسعار"""
        try:
            # التأكد من وجود الحقول قبل الوصول إليها
            if not hasattr(self, 'quantity') or not hasattr(self, 'purchase_price') or not hasattr(self, 'sale_price'):
                return
                
            if not hasattr(self, 'total_value_label') or not hasattr(self, 'profit_value_label'):
                return
            
            quantity = self.quantity.value()
            purchase_price = self.purchase_price.value()
            sale_price = self.sale_price.value()
            
            # حساب القيمة الإجمالية (الكمية × سعر الشراء)
            total_value = quantity * purchase_price
            self.total_value_label.setText(format_currency(total_value))

            # حساب المكسب المتوقع (الكمية × سعر البيع)
            profit_value = quantity * sale_price
            self.profit_value_label.setText(format_currency(profit_value))
            
        except Exception as e:
            # في حالة حدوث خطأ، عرض قيم افتراضية
            if hasattr(self, 'total_value_label'):
                self.total_value_label.setText(format_currency(0))
            if hasattr(self, 'profit_value_label'):
                self.profit_value_label.setText(format_currency(0))

    def load_product_data(self):
        """تحميل بيانات المنتج للتعديل"""
        if self.product:
            self.name_input.setText(self.product.name)
            self.code_input.setText(self.product.code or "")
            self.category_input.setText(self.product.category or "")
            self.unit_combo.setCurrentText(self.product.unit or "قطعة")
            self.barcode_input.setText(self.product.barcode or "")
            self.quantity.setValue(self.product.quantity)
            self.min_quantity.setValue(self.product.min_quantity)
            self.purchase_price.setValue(self.product.purchase_price)
            self.sale_price.setValue(self.product.sale_price)
            # تحميل الحد الأدنى للبيع إذا كان موجود
            if hasattr(self.product, 'min_sale_price') and self.product.min_sale_price:
                self.min_sale_price.setValue(self.product.min_sale_price)
            else:
                # إذا لم يكن موجود، استخدم سعر الشراء كحد أدنى افتراضي
                self.min_sale_price.setValue(self.product.purchase_price)
            self.description_input.setPlainText(self.product.description or "")

            # تحميل الباركودات الإضافية
            try:
                with Session(self.engine) as session:
                    barcodes = session.query(ProductBarcode).filter(
                        ProductBarcode.product_id == self.product.id
                    ).all()

                    # مسح الحقول أولاً
                    self.barcode_2_input.clear()
                    self.barcode_3_input.clear()
                    self.barcode_4_input.clear()

                    # تحميل الباركودات (بدون الأساسي)
                    additional_barcodes = [b.barcode for b in barcodes if not b.is_primary]

                    if len(additional_barcodes) > 0:
                        self.barcode_2_input.setText(additional_barcodes[0])
                    if len(additional_barcodes) > 1:
                        self.barcode_3_input.setText(additional_barcodes[1])
                    if len(additional_barcodes) > 2:
                        self.barcode_4_input.setText(additional_barcodes[2])

            except Exception as e:
                print(f"خطأ في تحميل الباركودات: {e}")

            # تحديث القيم المحسوبة
            self.update_calculated_values()

    def save_product(self):
        """حفظ المنتج"""
        if not self.name_input.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المنتج")
            return
            
        if self.purchase_price.value() <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال سعر شراء صحيح")
            return
            
        if self.sale_price.value() <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال سعر بيع صحيح")
            return

        # التحقق من أن سعر البيع أكبر من سعر الشراء
        if self.sale_price.value() <= self.purchase_price.value():
            QMessageBox.warning(
                self,
                "⚠️ تحذير: سعر غير مربح",
                f"لا يمكن حفظ المنتج!\n\n"
                f"💰 سعر الشراء: {self.purchase_price.value():,.0f} ج.م\n"
                f"🔴 سعر البيع المطلوب: {self.sale_price.value():,.0f} ج.م\n\n"
                f"⚠️ سعر البيع يجب أن يكون أكبر من سعر الشراء لضمان الربحية!\n"
                f"💡 الحد الأدنى المسموح: {self.purchase_price.value() + 1:,.0f} ج.م"
            )
            return
        
        try:
            with Session(self.engine) as session:
                if self.product:
                    # تعديل منتج موجود
                    product = session.merge(self.product)
                else:
                    # إضافة منتج جديد
                    product = Product()
                    session.add(product)
                
                # تحديث البيانات
                product.name = self.name_input.text().strip()
                product.code = self.code_input.text().strip() or None
                product.category = self.category_input.text().strip() or None
                product.unit = self.unit_combo.currentText()
                product.barcode = self.barcode_input.text().strip() or None
                product.quantity = self.quantity.value()
                product.min_quantity = self.min_quantity.value()
                product.purchase_price = self.purchase_price.value()
                product.sale_price = self.sale_price.value()
                # حفظ الحد الأدنى للبيع
                if hasattr(product, 'min_sale_price'):
                    product.min_sale_price = self.min_sale_price.value()
                product.description = self.description_input.toPlainText().strip() or None

                # حفظ المنتج أولاً للحصول على ID
                session.commit()
                session.refresh(product)

                # حفظ الباركودات المتعددة
                self.save_product_barcodes(session, product.id)

                # حفظ ID المنتج للاستخدام خارج النافذة
                self.saved_product_id = product.id

                session.commit()
                QMessageBox.information(self, "نجاح", "تم حفظ المنتج والباركودات بنجاح")
                self.accept()
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ المنتج:\n{str(e)}")

    def save_product_barcodes(self, session, product_id):
        """حفظ الباركودات المتعددة للمنتج"""
        try:
            # حذف الباركودات الموجودة (ما عدا الأساسي)
            session.query(ProductBarcode).filter(
                ProductBarcode.product_id == product_id
            ).delete()

            # جمع الباركودات الجديدة
            barcodes_to_save = []

            # الباركود الأساسي
            if self.barcode_input.text().strip():
                barcodes_to_save.append({
                    'barcode': self.barcode_input.text().strip(),
                    'description': 'الباركود الأساسي',
                    'is_primary': True
                })

            # الباركودات الإضافية
            if self.barcode_2_input.text().strip():
                barcodes_to_save.append({
                    'barcode': self.barcode_2_input.text().strip(),
                    'description': 'باركود إضافي 1',
                    'is_primary': False
                })

            if self.barcode_3_input.text().strip():
                barcodes_to_save.append({
                    'barcode': self.barcode_3_input.text().strip(),
                    'description': 'باركود إضافي 2',
                    'is_primary': False
                })

            if self.barcode_4_input.text().strip():
                barcodes_to_save.append({
                    'barcode': self.barcode_4_input.text().strip(),
                    'description': 'باركود إضافي 3',
                    'is_primary': False
                })

            # التحقق من عدم تكرار الباركودات
            unique_barcodes = set()
            for barcode_data in barcodes_to_save:
                barcode = barcode_data['barcode']
                if barcode in unique_barcodes:
                    QMessageBox.warning(
                        self,
                        "تحذير",
                        f"الباركود '{barcode}' مكرر!\nيرجى التأكد من أن كل باركود فريد."
                    )
                    return False
                unique_barcodes.add(barcode)

                # التحقق من عدم وجود الباركود في منتجات أخرى
                existing = session.query(ProductBarcode).filter(
                    ProductBarcode.barcode == barcode,
                    ProductBarcode.product_id != product_id
                ).first()

                if existing:
                    QMessageBox.warning(
                        self,
                        "تحذير",
                        f"الباركود '{barcode}' موجود بالفعل في منتج آخر!\nيرجى استخدام باركود مختلف."
                    )
                    return False

            # حفظ الباركودات الجديدة
            for barcode_data in barcodes_to_save:
                new_barcode = ProductBarcode(
                    product_id=product_id,
                    barcode=barcode_data['barcode'],
                    description=barcode_data['description'],
                    is_primary=barcode_data['is_primary']
                )
                session.add(new_barcode)

            return True

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء حفظ الباركودات:\n{str(e)}"
            )
            return False


class InventoryWidget(QWidget):
    """واجهة المخزون الجديدة مع عمود المكسب وتبويب إدارة الأصناف"""

    def __init__(self, engine):
        super().__init__()
        self.engine = engine
        self.setup_ui()
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_search)

    def is_dark_mode(self):
        """التحقق من الوضع الليلي"""
        # التحقق من الوضع الليلي من الواجهة الرئيسية
        if hasattr(self.parent(), 'is_dark_mode'):
            return self.parent().is_dark_mode
        return False

    def get_table_style(self, table_type="default"):
        """الحصول على تنسيق الجدول حسب الوضع"""
        if self.is_dark_mode():
            # ألوان الوضع الليلي
            return f"""
                QTableWidget {{
                    border: 2px solid #334155;
                    background-color: #0F172A;
                    gridline-color: #334155;
                    font-size: 16px;
                    color: #F8FAFC;
                    border-radius: 12px;
                }}
                QTableWidget::item {{
                    padding: 12px;
                    border-bottom: 1px solid #334155;
                    color: #F8FAFC;
                }}
                QTableWidget::item:selected {{
                    background-color: #6366F1;
                    color: #F8FAFC;
                }}
                QTableWidget::item:hover {{
                    background-color: rgba(99, 102, 241, 0.1);
                }}
                QHeaderView::section {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {'#6366F1' if table_type == 'inventory' else '#10B981'},
                        stop:1 {'#8B5CF6' if table_type == 'inventory' else '#34D399'});
                    color: #F8FAFC;
                    padding: 15px;
                    border: none;
                    font-weight: bold;
                    font-size: 18px;
                    border-radius: 8px;
                }}
                QHeaderView::section:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {'#4F46E5' if table_type == 'inventory' else '#059669'},
                        stop:1 {'#6366F1' if table_type == 'inventory' else '#10B981'});
                }}
            """
        else:
            # ألوان الوضع العادي
            return f"""
                QTableWidget {{
                    border: 1px solid #DEE2E6;
                    background-color: white;
                    gridline-color: #DEE2E6;
                    font-size: 16px;
                    color: #212529;
                    border-radius: 8px;
                }}
                QTableWidget::item {{
                    padding: 12px;
                    border-bottom: 1px solid #DEE2E6;
                    color: #212529;
                }}
                QTableWidget::item:selected {{
                    background-color: #E3F2FD;
                    color: #1976D2;
                }}
                QTableWidget::item:hover {{
                    background-color: rgba(25, 118, 210, 0.1);
                }}
                QHeaderView::section {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {'#3498DB' if table_type == 'inventory' else '#28A745'},
                        stop:1 {'#5DADE2' if table_type == 'inventory' else '#58D68D'});
                    color: white;
                    padding: 15px;
                    border: none;
                    font-weight: bold;
                    font-size: 18px;
                    border-radius: 8px;
                }}
                QHeaderView::section:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {'#2980B9' if table_type == 'inventory' else '#239B56'},
                        stop:1 {'#3498DB' if table_type == 'inventory' else '#28A745'});
                }}
            """

    def get_row_colors(self, row):
        """الحصول على ألوان الصفوف حسب الوضع"""
        if self.is_dark_mode():
            # ألوان الوضع الليلي
            return QColor("#1E293B") if row % 2 == 0 else QColor("#334155")
        else:
            # ألوان الوضع العادي
            return QColor("#F8F9FA") if row % 2 == 0 else QColor("#FFFFFF")

    def update_theme(self):
        """تحديث الألوان عند تغيير الوضع"""
        # تطبيق الثيم العام
        self.setStyleSheet(theme_manager.get_stylesheet("general"))

        # تحديث تنسيق الجداول
        self.products_table.setStyleSheet(theme_manager.get_stylesheet("table"))
        self.manage_products_table.setStyleSheet(theme_manager.get_stylesheet("table"))

        # تحديث الأزرار
        colors = theme_manager.get_colors()

        # تحديث إطار البحث
        if hasattr(self, 'search_input'):
            self.search_input.setStyleSheet(f"""
                QLineEdit {{
                    padding: 12px;
                    font-size: 16px;
                    border: 2px solid {colors['border_color']};
                    border-radius: 8px;
                    background-color: {colors['input_bg']};
                    color: {colors['input_text']};
                }}
                QLineEdit:focus {{
                    border-color: {colors['input_focus']};
                }}
            """)

        # تحديث إطار البحث في الإدارة
        if hasattr(self, 'manage_search_input'):
            self.manage_search_input.setStyleSheet(f"""
                QLineEdit {{
                    padding: 12px;
                    font-size: 16px;
                    border: 2px solid {colors['border_color']};
                    border-radius: 8px;
                    background-color: {colors['input_bg']};
                    color: {colors['input_text']};
                }}
                QLineEdit:focus {{
                    border-color: {colors['input_focus']};
                }}
            """)

        # إعادة تحميل البيانات لتطبيق الألوان الجديدة
        self.load_products()
        self.load_manage_products()

    def setup_ui(self):
        """إعداد واجهة المستخدم الرئيسية"""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        self.setLayout(layout)

        # إزالة العنوان غير الضروري لتوفير مساحة

        # إنشاء أزرار التبويبات بدلاً من QTabWidget
        tabs_frame = QFrame()
        tabs_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1E293B, stop:1 #334155);
                border: 3px solid #6366F1;
                border-radius: 15px;
                padding: 15px;
            }
        """)
        tabs_layout = QHBoxLayout()
        tabs_frame.setLayout(tabs_layout)

        # زر تبويب المخزون
        self.inventory_tab_btn = QPushButton("📦 المخزون")
        self.inventory_tab_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #10B981, stop:1 #34D399);
                color: #F8FAFC;
                font-size: 20px;
                font-weight: bold;
                padding: 20px 40px;
                border: 4px solid #059669;
                border-radius: 15px;
                min-width: 200px;
                min-height: 60px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #6366F1, stop:1 #8B5CF6);
                border-color: #4F46E5;
                transform: translateY(-3px);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4F46E5, stop:1 #6366F1);
                transform: translateY(0px);
            }
        """)
        self.inventory_tab_btn.clicked.connect(lambda: self.switch_tab(0))
        tabs_layout.addWidget(self.inventory_tab_btn)

        # زر تبويب إدارة الأصناف
        self.management_tab_btn = QPushButton("⚙️ إدارة الأصناف")
        self.management_tab_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #EF4444, stop:1 #F87171);
                color: #F8FAFC;
                font-size: 20px;
                font-weight: bold;
                padding: 20px 40px;
                border: 4px solid #DC2626;
                border-radius: 15px;
                min-width: 200px;
                min-height: 60px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #6366F1, stop:1 #8B5CF6);
                border-color: #4F46E5;
                transform: translateY(-3px);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4F46E5, stop:1 #6366F1);
                transform: translateY(0px);
            }
        """)
        self.management_tab_btn.clicked.connect(lambda: self.switch_tab(1))
        tabs_layout.addWidget(self.management_tab_btn)

        tabs_layout.addStretch()
        layout.addWidget(tabs_frame)

        # إنشاء التبويبات العادية (مخفية)
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("QTabBar::tab { height: 0px; }")  # إخفاء التبويبات الأصلية
        layout.addWidget(self.tab_widget)

        # إنشاء التبويبات
        self.create_inventory_tab()
        self.create_management_tab()

        # تحميل البيانات
        self.refresh_products()

        # تعيين التبويب الافتراضي
        self.switch_tab(0)

        # إضافة اختصار كيبورد لإظهار/إخفاء الإجماليات
        self.totals_shortcut = QShortcut(QKeySequence("Ctrl+T"), self)
        self.totals_shortcut.activated.connect(self.toggle_totals_visibility)

        # متغير لتتبع حالة الإجماليات
        self.totals_visible = False

    def toggle_totals_visibility(self):
        """تبديل إظهار/إخفاء قسم الإجماليات وعمود المكسب باستخدام Ctrl+T"""
        self.totals_visible = not self.totals_visible

        # إظهار/إخفاء قسم الإجماليات
        self.totals_frame.setVisible(self.totals_visible)

        # إظهار/إخفاء عمود المكسب في جدول إدارة الأصناف
        self.manage_products_table.setColumnHidden(7, not self.totals_visible)

    def switch_tab(self, index):
        """التبديل بين التبويبات مع تحديث ألوان الأزرار"""
        self.tab_widget.setCurrentIndex(index)

        if index == 0:  # تبويب المخزون
            self.inventory_tab_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #6366F1, stop:1 #8B5CF6);
                    color: #F8FAFC;
                    font-size: 20px;
                    font-weight: bold;
                    padding: 20px 40px;
                    border: 2px solid #4F46E5;
                    border-radius: 12px;
                    min-width: 200px;
                    min-height: 60px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #4F46E5, stop:1 #6366F1);
                    border-color: #3730A3;
                    transform: translateY(-2px);
                }
            """)
            self.management_tab_btn.setStyleSheet("""
                QPushButton {
                    background: #1E293B;
                    color: #CBD5E1;
                    font-size: 20px;
                    font-weight: bold;
                    padding: 20px 40px;
                    border: 2px solid #334155;
                    border-radius: 12px;
                    min-width: 200px;
                    min-height: 60px;
                }
                QPushButton:hover {
                    background: #334155;
                    border-color: #475569;
                    color: #F8FAFC;
            """)
        else:  # تبويب إدارة الأصناف
            self.management_tab_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #10B981, stop:1 #34D399);
                    color: #F8FAFC;
                    font-size: 20px;
                    font-weight: bold;
                    padding: 20px 40px;
                    border: 2px solid #059669;
                    border-radius: 12px;
                    min-width: 200px;
                    min-height: 60px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #059669, stop:1 #10B981);
                    border-color: #047857;
                    transform: translateY(-2px);
                }
            """)
            self.inventory_tab_btn.setStyleSheet("""
                QPushButton {
                    background: #1E293B;
                    color: #CBD5E1;
                    font-size: 20px;
                    font-weight: bold;
                    padding: 20px 40px;
                    border: 2px solid #334155;
                    border-radius: 12px;
                    min-width: 200px;
                    min-height: 60px;
                }
                QPushButton:hover {
                    background: #334155;
                    border-color: #475569;
                    color: #F8FAFC;
                    transform: translateY(-2px);
                }
            """)

    def create_inventory_tab(self):
        """إنشاء تبويب عرض المخزون"""
        inventory_tab = QWidget()
        inventory_layout = QVBoxLayout()
        inventory_tab.setLayout(inventory_layout)

        # منطقة البحث والأزرار
        search_frame = QFrame()
        search_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border-radius: 10px;
                padding: 15px;
                margin: 10px 0;
            }
        """)
        search_layout = QHBoxLayout()
        search_frame.setLayout(search_layout)

        # حقل البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("🔍 بحث عن منتج (الاسم، الكود، الفئة)...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 12px;
                border: 2px solid #DEE2E6;
                border-radius: 8px;
                background-color: white;
                font-size: 16px;
                min-width: 300px;
            }
            QLineEdit:focus {
                border-color: #3498DB;
            }
        """)
        self.search_input.textChanged.connect(self.on_search_changed)
        search_layout.addWidget(self.search_input)

        # زر تحديث
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 12px 25px;
                border: none;
                border-radius: 8px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        refresh_btn.clicked.connect(self.refresh_products)
        search_layout.addWidget(refresh_btn)

        # زر إضافة منتج جديد
        add_product_btn = QPushButton("➕ إضافة منتج جديد")
        add_product_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 12px 25px;
                border: none;
                border-radius: 8px;
                min-width: 180px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        add_product_btn.clicked.connect(self.show_add_product_dialog)
        search_layout.addWidget(add_product_btn)

        # زر مسح بالكاميرا
        camera_scan_btn = QPushButton("📷 مسح بالكاميرا")
        camera_scan_btn.setStyleSheet("""
            QPushButton {
                background-color: #17A2B8;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 12px 25px;
                border: none;
                border-radius: 8px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        camera_scan_btn.clicked.connect(self.scan_barcode_camera)
        search_layout.addWidget(camera_scan_btn)

        # زر مسح بالجهاز
        device_scan_btn = QPushButton("🔍 مسح بالجهاز")
        device_scan_btn.setStyleSheet("""
            QPushButton {
                background-color: #6F42C1;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 12px 25px;
                border: none;
                border-radius: 8px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #5A2D91;
            }
        """)
        device_scan_btn.clicked.connect(self.scan_barcode_device)
        search_layout.addWidget(device_scan_btn)

        search_layout.addStretch()
        inventory_layout.addWidget(search_frame)

        # جدول المنتجات
        self.products_table = QTableWidget()
        # تطبيق التنسيق حسب الوضع
        self.products_table.setStyleSheet(self.get_table_style("inventory"))

        # إعداد أعمدة الجدول مع عمود المكسب الجديد
        self.products_table.setColumnCount(8)
        self.products_table.setHorizontalHeaderLabels([
            "الكود", "اسم المنتج", "الوحدة", "سعر الشراء",
            "سعر البيع", "الكمية", "القيمة", "💰 المكسب"
        ])

        # تنسيق عرض الجدول
        header = self.products_table.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # جعل عمود الاسم مرناً
        for i in [0, 2, 3, 4, 5, 6, 7]:  # تحديد عرض ثابت لباقي الأعمدة
            header.setSectionResizeMode(i, QHeaderView.Fixed)
            header.resizeSection(i, 140)  # زيادة العرض لاستيعاب الخط الأكبر

        # تعديل ارتفاع الصفوف ليناسب الخط المحسن
        self.products_table.verticalHeader().setDefaultSectionSize(90)
        
        # تكبير حجم الخط في الجدول
        self.products_table.setStyleSheet("""
            QTableWidget {
                font-size: 16px;
                font-weight: bold;
                gridline-color: #D3D3D3;
                background-color: white;
                alternate-background-color: #F8F9FA;
                selection-background-color: #0078D4;
                selection-color: white;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #E0E0E0;
            }
            QTableWidget::item:selected {
                background-color: #0078D4;
                color: white;
            }
            QHeaderView::section {
                background-color: #2C3E50;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 12px;
                border: none;
            }
        """)

        inventory_layout.addWidget(self.products_table)

        # إضافة التبويب
        self.tab_widget.addTab(inventory_tab, "📦 المخزون")

    def create_management_tab(self):
        """إنشاء تبويب إدارة الأصناف"""
        management_tab = QWidget()
        management_layout = QVBoxLayout()
        management_tab.setLayout(management_layout)

        # منطقة البحث والأزرار
        manage_search_frame = QFrame()
        manage_search_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border-radius: 10px;
                padding: 15px;
                margin: 10px 0;
            }
        """)
        manage_search_layout = QHBoxLayout()
        manage_search_frame.setLayout(manage_search_layout)

        # حقل البحث
        self.manage_search_input = QLineEdit()
        self.manage_search_input.setPlaceholderText("🔍 بحث عن صنف للتعديل أو الحذف...")
        self.manage_search_input.setStyleSheet("""
            QLineEdit {
                padding: 12px;
                border: 2px solid #DEE2E6;
                border-radius: 8px;
                background-color: white;
                font-size: 16px;
                min-width: 300px;
            }
            QLineEdit:focus {
                border-color: #3498DB;
            }
        """)
        self.manage_search_input.textChanged.connect(lambda text: self.perform_search(text, self.manage_products_table))
        manage_search_layout.addWidget(self.manage_search_input)

        # زر إضافة صنف جديد
        add_item_btn = QPushButton("➕ إضافة صنف جديد")
        add_item_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 12px 25px;
                border: none;
                border-radius: 8px;
                min-width: 180px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        add_item_btn.clicked.connect(self.show_add_product_dialog)
        manage_search_layout.addWidget(add_item_btn)

        manage_search_layout.addStretch()
        management_layout.addWidget(manage_search_frame)

        # جدول إدارة الأصناف
        self.manage_products_table = QTableWidget()
        # تطبيق التنسيق حسب الوضع
        self.manage_products_table.setStyleSheet(self.get_table_style("management"))

        # إعداد أعمدة الجدول مع عمود المكسب وأزرار التحكم
        self.manage_products_table.setColumnCount(10)
        self.manage_products_table.setHorizontalHeaderLabels([
            "الكود", "اسم الصنف", "الوحدة", "سعر الشراء",
            "سعر البيع", "الكمية", "القيمة", "💰 المكسب", "✏️ تعديل", "🗑️ حذف"
        ])

        # تنسيق عرض جدول الإدارة
        manage_header = self.manage_products_table.horizontalHeader()
        manage_header.setSectionResizeMode(1, QHeaderView.Stretch)  # جعل عمود الاسم مرناً

        # تحديد عرض الأعمدة بدقة لتجنب التداخل
        manage_header.setSectionResizeMode(0, QHeaderView.Fixed)  # الكود
        manage_header.resizeSection(0, 100)

        manage_header.setSectionResizeMode(2, QHeaderView.Fixed)  # الوحدة
        manage_header.resizeSection(2, 100)

        manage_header.setSectionResizeMode(3, QHeaderView.Fixed)  # سعر الشراء
        manage_header.resizeSection(3, 180)  # زيادة العرض لاستيعاب الخط الكبير

        manage_header.setSectionResizeMode(4, QHeaderView.Fixed)  # سعر البيع
        manage_header.resizeSection(4, 180)  # زيادة العرض لاستيعاب الخط الكبير

        manage_header.setSectionResizeMode(5, QHeaderView.Fixed)  # الكمية
        manage_header.resizeSection(5, 100)

        manage_header.setSectionResizeMode(6, QHeaderView.Fixed)  # القيمة
        manage_header.resizeSection(6, 200)  # زيادة العرض لاستيعاب الخط الكبير

        manage_header.setSectionResizeMode(7, QHeaderView.Fixed)  # المكسب
        manage_header.resizeSection(7, 200)  # زيادة العرض لاستيعاب الخط الكبير

        manage_header.setSectionResizeMode(8, QHeaderView.Fixed)  # تعديل
        manage_header.resizeSection(8, 80)

        manage_header.setSectionResizeMode(9, QHeaderView.Fixed)  # حذف
        manage_header.resizeSection(9, 100)  # زيادة العرض

        # تعديل ارتفاع الصفوف ليناسب الخط المحسن
        self.manage_products_table.verticalHeader().setDefaultSectionSize(90)
        
        # تكبير حجم الخط في الجدول
        self.manage_products_table.setStyleSheet("""
            QTableWidget {
                font-size: 16px;
                font-weight: bold;
                gridline-color: #D3D3D3;
                background-color: white;
                alternate-background-color: #F8F9FA;
                selection-background-color: #0078D4;
                selection-color: white;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #E0E0E0;
            }
            QTableWidget::item:selected {
                background-color: #0078D4;
                color: white;
            }
            QHeaderView::section {
                background-color: #2C3E50;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 12px;
                border: none;
            }
        """)

        management_layout.addWidget(self.manage_products_table)

        # إضافة قسم الإجماليات
        totals_frame = QFrame()
        totals_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border: 2px solid #DEE2E6;
                border-radius: 10px;
                padding: 15px;
                margin: 10px 0;
            }
        """)
        totals_layout = QHBoxLayout()
        totals_frame.setLayout(totals_layout)

        # إجمالي سعر الشراء
        self.total_purchase_label = QLabel("إجمالي سعر الشراء: 0.00 ريال")
        self.total_purchase_label.setStyleSheet("""
            QLabel {
                background-color: #E3F2FD;
                border: 2px solid #2196F3;
                border-radius: 8px;
                padding: 15px;
                font-size: 16px;
                font-weight: bold;
                color: #1976D2;
                min-width: 200px;
                text-align: center;
            }
        """)
        self.total_purchase_label.setAlignment(Qt.AlignCenter)
        totals_layout.addWidget(self.total_purchase_label)

        # إجمالي سعر البيع
        self.total_sale_label = QLabel("إجمالي سعر البيع: 0.00 ريال")
        self.total_sale_label.setStyleSheet("""
            QLabel {
                background-color: #FFF3E0;
                border: 2px solid #FF9800;
                border-radius: 8px;
                padding: 15px;
                font-size: 16px;
                font-weight: bold;
                color: #F57C00;
                min-width: 200px;
                text-align: center;
            }
        """)
        self.total_sale_label.setAlignment(Qt.AlignCenter)
        totals_layout.addWidget(self.total_sale_label)

        # إجمالي المكسب
        self.total_profit_label = QLabel("إجمالي المكسب: 0.00 ريال")
        self.total_profit_label.setStyleSheet("""
            QLabel {
                background-color: #E8F5E8;
                border: 2px solid #4CAF50;
                border-radius: 8px;
                padding: 15px;
                font-size: 18px;
                font-weight: bold;
                color: #2E7D32;
                min-width: 200px;
                text-align: center;
            }
        """)
        self.total_profit_label.setAlignment(Qt.AlignCenter)
        totals_layout.addWidget(self.total_profit_label)

        # حفظ مرجع لإطار الإجماليات
        self.totals_frame = totals_frame

        # إخفاء الإجماليات وعمود المكسب افتراضياً
        self.totals_frame.setVisible(False)
        self.manage_products_table.setColumnHidden(7, True)  # إخفاء عمود المكسب

        management_layout.addWidget(totals_frame)

        # إضافة التبويب
        self.tab_widget.addTab(management_tab, "⚙️ إدارة الأصناف")

    def show_add_product_dialog(self):
        """عرض نافذة إضافة منتج جديد"""
        dialog = ProductDialog(self.engine, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_products()

    def edit_product(self, product):
        """تعديل منتج موجود"""
        dialog = ProductDialog(self.engine, product, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_products()

    def delete_product(self, product):
        """حذف منتج مع تأكيد"""
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف المنتج '{product.name}'؟\n\n"
            f"سيتم حذف جميع البيانات المرتبطة بهذا المنتج.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                with Session(self.engine) as session:
                    product = session.merge(product)
                    session.delete(product)
                    session.commit()
                self.refresh_products()
                QMessageBox.information(self, "نجاح", "تم حذف المنتج بنجاح")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف المنتج:\n{str(e)}")

    def on_search_changed(self):
        """تأخير البحث لتحسين الأداء"""
        self.search_timer.start(300)

    def perform_search(self, search_text=None, table=None):
        """تنفيذ البحث في المنتجات"""
        if search_text is None:
            search_text = self.search_input.text().strip()

        with Session(self.engine) as session:
            if search_text:
                # البحث في الحقول الأساسية والباركودات المتعددة
                products_by_basic = session.query(Product).filter(
                    or_(
                        Product.name.ilike(f"%{search_text}%"),
                        Product.code.ilike(f"%{search_text}%"),
                        Product.category.ilike(f"%{search_text}%"),
                        Product.barcode.ilike(f"%{search_text}%")
                    )
                ).all()

                # البحث في الباركودات المتعددة
                products_by_barcode = session.query(Product).join(ProductBarcode).filter(
                    ProductBarcode.barcode.ilike(f"%{search_text}%")
                ).all()

                # دمج النتائج وإزالة التكرار
                products_dict = {}
                for product in products_by_basic + products_by_barcode:
                    products_dict[product.id] = product
                products = list(products_dict.values())
            else:
                products = session.query(Product).all()

            # تحديث الجدول المناسب
            if table == self.manage_products_table:
                self.update_management_table(products)
            else:
                self.update_inventory_table(products)

    def refresh_products(self):
        """تحديث جداول المنتجات"""
        with Session(self.engine) as session:
            products = session.query(Product).all()
            self.update_inventory_table(products)  # جدول المخزون (بدون مكسب)
            self.update_management_table(products)  # جدول الإدارة (مع مكسب وأزرار)

    def update_table(self, products, table, include_actions=False):
        """تحديث جدول المنتجات مع عمود المكسب"""
        table.setRowCount(len(products))

        for row, product in enumerate(products):
            # تعيين لون الخلفية للصف حسب الوضع
            bg_color = self.get_row_colors(row)

            # إضافة بيانات المنتج
            table.setItem(row, 0, self.create_table_item(product.code or "", bg_color))
            table.setItem(row, 1, self.create_table_item(product.name, bg_color))
            table.setItem(row, 2, self.create_table_item(product.unit or "", bg_color))
            table.setItem(row, 3, self.create_table_item(f"{product.purchase_price:,.0f}", bg_color))
            table.setItem(row, 4, self.create_table_item(f"{product.sale_price:,.0f}", bg_color))
            table.setItem(row, 5, self.create_table_item(str(product.quantity), bg_color))

            # حساب وإضافة القيمة الإجمالية (الكمية × سعر الشراء)
            total_value = product.quantity * product.purchase_price
            value_item = self.create_table_item(f"{total_value:,.0f}", bg_color)
            value_item.setTextAlignment(Qt.AlignCenter)
            table.setItem(row, 6, value_item)

            # حساب وإضافة المكسب المتوقع (الكمية × سعر البيع) - العمود الجديد
            profit_value = product.quantity * product.sale_price
            profit_item = self.create_table_item(f"{profit_value:,.0f}", QColor("#0F172A"))
            profit_item.setTextAlignment(Qt.AlignCenter)
            # تلوين المكسب بلون أخضر عصري مميز
            profit_item.setForeground(QColor("#10B981"))
            table.setItem(row, 7, profit_item)

            # إضافة أزرار التحكم في تبويب إدارة الأصناف
            if include_actions:
                # زر التعديل
                edit_btn = QPushButton("✏️ تعديل")
                edit_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #FFC107;
                        color: #212529;
                        border: none;
                        padding: 8px 15px;
                        border-radius: 5px;
                        font-weight: bold;
                        font-size: 12px;
                    }
                    QPushButton:hover {
                        background-color: #E0A800;
                    }
                """)
                edit_btn.clicked.connect(lambda checked, p=product: self.edit_product(p))
                table.setCellWidget(row, 8, edit_btn)

                # زر الحذف
                delete_btn = QPushButton("🗑️ حذف")
                delete_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #DC3545;
                        color: white;
                        border: none;
                        padding: 8px 15px;
                        border-radius: 5px;
                        font-weight: bold;
                        font-size: 12px;
                    }
                    QPushButton:hover {
                        background-color: #C82333;
                    }
                """)
                delete_btn.clicked.connect(lambda checked, p=product: self.delete_product(p))
                table.setCellWidget(row, 9, delete_btn)

    def update_inventory_table(self, products):
        """تحديث جدول المخزون (بدون عمود المكسب وبدون أزرار)"""
        self.products_table.setRowCount(len(products))

        for row, product in enumerate(products):
            # تعيين لون الخلفية للصف حسب الوضع
            bg_color = self.get_row_colors(row)

            # إضافة بيانات المنتج (7 أعمدة فقط)
            self.products_table.setItem(row, 0, self.create_table_item(product.code or "", bg_color))
            self.products_table.setItem(row, 1, self.create_table_item(product.name, bg_color))
            self.products_table.setItem(row, 2, self.create_table_item(product.unit or "", bg_color))
            self.products_table.setItem(row, 3, self.create_table_item(f"{product.purchase_price:,.0f}", bg_color))
            self.products_table.setItem(row, 4, self.create_table_item(f"{product.sale_price:,.0f}", bg_color))
            self.products_table.setItem(row, 5, self.create_table_item(str(product.quantity), bg_color))

            # حساب القيمة الإجمالية
            total_value = product.quantity * product.purchase_price
            self.products_table.setItem(row, 6, self.create_table_item(f"{total_value:,.0f}", bg_color))

    def update_management_table(self, products):
        """تحديث جدول إدارة الأصناف (مع عمود المكسب والأزرار)"""
        self.manage_products_table.setRowCount(len(products))

        for row, product in enumerate(products):
            # تعيين لون الخلفية للصف حسب الوضع
            bg_color = self.get_row_colors(row)

            # إضافة بيانات المنتج (10 أعمدة)
            self.manage_products_table.setItem(row, 0, self.create_table_item(product.code or "", bg_color))
            self.manage_products_table.setItem(row, 1, self.create_table_item(product.name, bg_color))
            self.manage_products_table.setItem(row, 2, self.create_table_item(product.unit or "", bg_color))
            self.manage_products_table.setItem(row, 3, self.create_table_item(f"{product.purchase_price:,.0f}", bg_color))
            self.manage_products_table.setItem(row, 4, self.create_table_item(f"{product.sale_price:,.0f}", bg_color))
            self.manage_products_table.setItem(row, 5, self.create_table_item(str(product.quantity), bg_color))

            # حساب القيمة الإجمالية
            total_value = product.quantity * product.purchase_price
            self.manage_products_table.setItem(row, 6, self.create_table_item(f"{total_value:,.0f}", bg_color))

            # حساب المكسب (الكمية × سعر البيع) - بلون أخضر عصري
            profit = product.quantity * product.sale_price
            profit_item = self.create_table_item(f"{profit:,.0f}", QColor("#0F172A"))
            profit_item.setForeground(QColor("#10B981"))
            self.manage_products_table.setItem(row, 7, profit_item)

            # زر التعديل - أكبر وأوضح
            edit_btn = QPushButton("✏️")
            edit_btn.setStyleSheet("""
                QPushButton {
                    background-color: #FFC107;
                    color: #212529;
                    border: none;
                    padding: 8px;
                    border-radius: 6px;
                    font-weight: bold;
                    font-size: 16px;
                    min-width: 60px;
                    min-height: 35px;
                    margin: 2px;
                }
                QPushButton:hover {
                    background-color: #E0A800;
                }
            """)
            edit_btn.setToolTip("تعديل المنتج")
            edit_btn.clicked.connect(lambda checked, p=product: self.edit_product(p))
            self.manage_products_table.setCellWidget(row, 8, edit_btn)

            # زر الحذف - أكبر وأوضح
            delete_btn = QPushButton("🗑️")
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #DC3545;
                    color: white;
                    border: none;
                    padding: 8px;
                    border-radius: 6px;
                    font-weight: bold;
                    font-size: 16px;
                    min-width: 60px;
                    min-height: 35px;
                    margin: 2px;
                }
                QPushButton:hover {
                    background-color: #C82333;
                }
            """)
            delete_btn.setToolTip("حذف المنتج")
            delete_btn.clicked.connect(lambda checked, p=product: self.delete_product(p))
            self.manage_products_table.setCellWidget(row, 9, delete_btn)

        # تحديث الإجماليات
        self.update_totals(products)

    def update_totals(self, products):
        """تحديث إجماليات المخزون"""
        total_purchase_value = 0
        total_sale_value = 0

        for product in products:
            # إجمالي قيمة الشراء (الكمية × سعر الشراء)
            total_purchase_value += product.quantity * product.purchase_price

            # إجمالي قيمة البيع (الكمية × سعر البيع)
            total_sale_value += product.quantity * product.sale_price

        # حساب إجمالي المكسب
        total_profit = total_sale_value - total_purchase_value

        # تحديث التسميات
        self.total_purchase_label.setText(f"إجمالي سعر الشراء: {format_currency(total_purchase_value)}")
        self.total_sale_label.setText(f"إجمالي سعر البيع: {format_currency(total_sale_value)}")
        self.total_profit_label.setText(f"إجمالي المكسب: {format_currency(total_profit)}")

        # تغيير لون المكسب حسب القيمة
        if total_profit > 0:
            profit_color = "#2E7D32"  # أخضر للمكسب الإيجابي
            bg_color = "#E8F5E8"
        elif total_profit < 0:
            profit_color = "#D32F2F"  # أحمر للخسارة
            bg_color = "#FFEBEE"
        else:
            profit_color = "#757575"  # رمادي للصفر
            bg_color = "#F5F5F5"

        self.total_profit_label.setStyleSheet(f"""
            QLabel {{
                background-color: {bg_color};
                border: 2px solid {profit_color};
                border-radius: 8px;
                padding: 15px;
                font-size: 18px;
                font-weight: bold;
                color: {profit_color};
                min-width: 200px;
                text-align: center;
            }}
        """)

    def create_table_item(self, text, bg_color):
        """إنشاء عنصر جدول مع تنسيق"""
        item = QTableWidgetItem(str(text))
        item.setBackground(bg_color)
        item.setTextAlignment(Qt.AlignCenter)
        return item

    def scan_barcode_camera(self):
        """مسح الباركود بالكاميرا"""
        try:
            from .barcode_scanner import BarcodeScannerDialog
            scanner = BarcodeScannerDialog(self)
            scanner.barcode_detected.connect(self.on_barcode_detected)
            scanner.exec_()
        except ImportError:
            QMessageBox.warning(
                self,
                "مكتبات مفقودة",
                "لاستخدام قارئ الباركود بالكاميرا، يجب تثبيت المكتبات التالية:\n"
                "pip install opencv-python pyzbar"
            )
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح قارئ الباركود:\n{str(e)}")

    def scan_barcode_device(self):
        """مسح الباركود بالجهاز المحمول"""
        try:
            from .barcode_device_scanner import BarcodeDeviceScannerDialog
            scanner = BarcodeDeviceScannerDialog(self)
            scanner.barcode_detected.connect(self.on_barcode_detected)
            scanner.exec_()
        except ImportError:
            self.show_barcode_input_dialog()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح قارئ الباركود:\n{str(e)}")

    def show_barcode_input_dialog(self):
        """نافذة بسيطة لإدخال الباركود"""
        from PyQt5.QtWidgets import QInputDialog
        barcode, ok = QInputDialog.getText(self, "إدخال الباركود", "امسح أو أدخل الباركود:")
        if ok and barcode.strip():
            self.on_barcode_detected(barcode.strip())

    def on_barcode_detected(self, barcode):
        """معالجة الباركود المكتشف"""
        with Session(self.engine) as session:
            product = session.query(Product).filter(Product.barcode == barcode).first()

            if product:
                QMessageBox.information(
                    self,
                    "✅ منتج موجود",
                    f"تم العثور على المنتج:\n\n"
                    f"الاسم: {product.name}\n"
                    f"الكود: {product.code or 'غير محدد'}\n"
                    f"الكمية: {product.quantity}\n"
                    f"سعر البيع: {product.sale_price:,.0f} ج.م"
                )
                self.highlight_product_in_table(product)
            else:
                reply = QMessageBox.question(
                    self,
                    "❓ منتج غير موجود",
                    f"لم يتم العثور على منتج بالباركود:\n{barcode}\n\n"
                    "هل تريد إضافة منتج جديد بهذا الباركود؟",
                    QMessageBox.Yes | QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    dialog = ProductDialog(self.engine, parent=self)
                    dialog.barcode_input.setText(barcode)
                    if dialog.exec_() == QDialog.Accepted:
                        self.refresh_products()

    def highlight_product_in_table(self, product):
        """تمييز المنتج في الجدول"""
        for row in range(self.products_table.rowCount()):
            name_item = self.products_table.item(row, 1)
            if name_item and name_item.text() == product.name:
                for col in range(self.products_table.columnCount()):
                    item = self.products_table.item(row, col)
                    if item:
                        item.setBackground(QColor("#FFE4B5"))
                self.products_table.scrollToItem(name_item)
                self.products_table.selectRow(row)
                break
