// qiconengine.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QIconEngine /Supertype=sip.wrapper/
{
%TypeHeaderCode
#include <qiconengine.h>
%End

public:
%If (Qt_5_6_0 -)
    QIconEngine();
%End
%If (Qt_5_8_0 -)
    QIconEngine(const QIconEngine &other);
%End
    virtual ~QIconEngine();
    virtual void paint(QPainter *painter, const QRect &rect, QIcon::Mode mode, QIcon::State state) = 0;
    virtual QSize actualSize(const QSize &size, QIcon::Mode mode, QIcon::State state);
    virtual QPixmap pixmap(const QSize &size, QIcon::Mode mode, QIcon::State state);
    virtual void addPixmap(const QPixmap &pixmap, QIcon::Mode mode, QIcon::State state);
    virtual void addFile(const QString &fileName, const QSize &size, QIcon::Mode mode, QIcon::State state);
    virtual QString key() const;
    virtual QIconEngine *clone() const = 0 /Factory/;
    virtual bool read(QDataStream &in);
    virtual bool write(QDataStream &out) const;

    enum IconEngineHook
    {
        AvailableSizesHook,
        IconNameHook,
%If (Qt_5_7_0 -)
        IsNullHook,
%End
%If (Qt_5_9_0 -)
        ScaledPixmapHook,
%End
    };

    struct AvailableSizesArgument
    {
%TypeHeaderCode
#include <qiconengine.h>
%End

        QIcon::Mode mode;
        QIcon::State state;
        QList<QSize> sizes;
    };

    virtual QList<QSize> availableSizes(QIcon::Mode mode = QIcon::Normal, QIcon::State state = QIcon::Off) const;
    virtual QString iconName() const;
%If (Qt_5_7_0 -)
    bool isNull() const;
%End
%If (Qt_5_9_0 -)
    QPixmap scaledPixmap(const QSize &size, QIcon::Mode mode, QIcon::State state, qreal scale);
%End
%If (Qt_5_9_0 -)

    struct ScaledPixmapArgument
    {
%TypeHeaderCode
#include <qiconengine.h>
%End

        QSize size;
        QIcon::Mode mode;
        QIcon::State state;
        qreal scale;
        QPixmap pixmap;
    };

%End
};
