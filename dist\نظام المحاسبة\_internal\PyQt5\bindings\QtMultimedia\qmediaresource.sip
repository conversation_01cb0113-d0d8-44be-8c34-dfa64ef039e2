// qmediaresource.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QMediaResource
{
%TypeHeaderCode
#include <qmediaresource.h>
%End

public:
    QMediaResource();
    QMediaResource(const QUrl &url, const QString &mimeType = QString());
    QMediaResource(const QNetworkRequest &request, const QString &mimeType = QString());
    QMediaResource(const QMediaResource &other);
    ~QMediaResource();
    bool isNull() const;
    bool operator==(const QMediaResource &other) const;
    bool operator!=(const QMediaResource &other) const;
    QUrl url() const;
    QNetworkRequest request() const;
    QString mimeType() const;
    QString language() const;
    void setLanguage(const QString &language);
    QString audioCodec() const;
    void setAudioCodec(const QString &codec);
    QString videoCodec() const;
    void setVideoCodec(const QString &codec);
    qint64 dataSize() const;
    void setDataSize(const qint64 size);
    int audioBitRate() const;
    void setAudioBitRate(int rate);
    int sampleRate() const;
    void setSampleRate(int frequency);
    int channelCount() const;
    void setChannelCount(int channels);
    int videoBitRate() const;
    void setVideoBitRate(int rate);
    QSize resolution() const;
    void setResolution(const QSize &resolution);
    void setResolution(int width, int height);
};

typedef QList<QMediaResource> QMediaResourceList;
