#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حذف جميع المنتجات من قاعدة البيانات بأمان - إصدار محسن
"""

import sys
import os
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.models import Product

def delete_all_products_simple():
    """حذف جميع المنتجات من قاعدة البيانات بطريقة بسيطة"""
    
    try:
        engine = create_engine('sqlite:///accounting.db')
        Session = sessionmaker(bind=engine)
        
        with Session() as session:
            # عد المنتجات قبل الحذف
            products_count = session.query(Product).count()
            print(f"📊 عدد المنتجات قبل الحذف: {products_count}")
            
            if products_count == 0:
                print("✅ لا توجد منتجات للحذف")
                return
            
            # حذف الباركودات المرتبطة أولاً
            print("🗑️ حذف الباركودات المرتبطة...")
            try:
                deleted_barcodes = session.execute(text("""
                    DELETE FROM product_barcodes
                """)).rowcount
                print(f"✅ تم حذف {deleted_barcodes} باركود")
            except Exception as e:
                print(f"⚠️ تحذير في حذف الباركودات: {e}")
            
            # حذف جميع المنتجات
            print("🗑️ حذف جميع المنتجات...")
            deleted_products = session.execute(text("""
                DELETE FROM products
            """)).rowcount
            
            # محاولة إعادة تعيين العداد التلقائي (اختياري)
            try:
                session.execute(text("""
                    UPDATE sqlite_sequence SET seq = 0 WHERE name = 'products'
                """))
                print("✅ تم إعادة تعيين العداد التلقائي")
            except Exception as e:
                print(f"⚠️ تحذير في إعادة تعيين العداد: {e}")
            
            # حفظ التغييرات
            session.commit()
            
            print(f"✅ تم حذف {deleted_products} منتج بنجاح")
            
            # التحقق من النتيجة
            remaining_count = session.query(Product).count()
            print(f"📊 عدد المنتجات بعد الحذف: {remaining_count}")
            
            if remaining_count == 0:
                print("🎉 تم حذف جميع المنتجات بنجاح!")
            else:
                print(f"⚠️ تبقى {remaining_count} منتج لم يتم حذفه")
                
    except Exception as e:
        print(f"❌ خطأ في حذف المنتجات: {e}")
        print(f"نوع الخطأ: {type(e).__name__}")

def main():
    """الدالة الرئيسية"""
    print("🗑️ حذف جميع المنتجات من قاعدة البيانات")
    print("=" * 50)
    
    # تنفيذ الحذف
    delete_all_products_simple()
    
    print("\n" + "=" * 50)
    print("✅ انتهت عملية الحذف")

if __name__ == "__main__":
    main()
