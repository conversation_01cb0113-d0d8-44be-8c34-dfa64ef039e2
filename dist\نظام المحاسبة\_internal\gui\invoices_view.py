from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
    QPushButton, QLineEdit, QLabel,
    QFrame, QGridLayout, QHeaderView, QDateEdit, QMessageBox, QDialog, QTableWidgetItem, QAbstractItemView
)
from PyQt5.QtCore import QDate, pyqtSignal
from PyQt5.QtGui import QColor
from sqlalchemy.orm import Session
from database.models import Transaction, Customer, TransactionType
from datetime import datetime
from utils.currency_formatter import format_currency, format_number
from utils.dialog_utils import setup_large_dialog

class InvoicesViewWidget(QWidget):
    def __init__(self, engine):
        super().__init__()
        self.engine = engine
        self.setup_ui()
        self.load_invoices()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # عنوان الصفحة
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(41, 128, 185, 0.1);
                border: 2px solid white;
                border-radius: 10px;
                padding: 20px;
                margin-bottom: 10px;
            }
            QLabel { color: white; font-weight: bold; }
        """)
        header_layout = QHBoxLayout()
        header_frame.setLayout(header_layout)
        
        # أيقونة الفواتير
        icon_label = QLabel("📋")
        icon_label.setStyleSheet("font-size: 48px; color: white;")
        
        # عنوان الصفحة
        title_label = QLabel("فواتير المبيعات")
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: white;")
        
        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        layout.addWidget(header_frame)
        
        # شريط البحث والفلترة
        filter_frame = QFrame()
        filter_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border-radius: 5px;
                padding: 15px;
                margin-bottom: 10px;
            }
        """)
        filter_layout = QHBoxLayout()
        filter_frame.setLayout(filter_layout)
        
        # البحث برقم الفاتورة
        filter_layout.addWidget(QLabel("رقم الفاتورة:"))
        self.invoice_search = QLineEdit()
        self.invoice_search.setPlaceholderText("ابحث برقم الفاتورة...")
        self.invoice_search.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #3498DB;
                border-radius: 5px;
                min-width: 150px;
            }
        """)
        self.invoice_search.textChanged.connect(self.filter_invoices)
        filter_layout.addWidget(self.invoice_search)
        
        # البحث باسم العميل
        filter_layout.addWidget(QLabel("العميل:"))
        self.customer_search = QLineEdit()
        self.customer_search.setPlaceholderText("ابحث باسم العميل...")
        self.customer_search.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #3498DB;
                border-radius: 5px;
                min-width: 150px;
            }
        """)
        self.customer_search.textChanged.connect(self.filter_invoices)
        filter_layout.addWidget(self.customer_search)
        
        # فلتر التاريخ
        filter_layout.addWidget(QLabel("من تاريخ:"))
        self.date_from = QDateEdit()
        self.date_from.setDate(QDate.currentDate().addDays(-30))
        self.date_from.setStyleSheet("""
            QDateEdit {
                padding: 8px;
                border: 2px solid #3498DB;
                border-radius: 5px;
            }
        """)
        self.date_from.dateChanged.connect(self.filter_invoices)
        filter_layout.addWidget(self.date_from)
        
        filter_layout.addWidget(QLabel("إلى تاريخ:"))
        self.date_to = QDateEdit()
        self.date_to.setDate(QDate.currentDate())
        self.date_to.setStyleSheet("""
            QDateEdit {
                padding: 8px;
                border: 2px solid #3498DB;
                border-radius: 5px;
            }
        """)
        self.date_to.dateChanged.connect(self.filter_invoices)
        filter_layout.addWidget(self.date_to)
        
        # زر إعادة تحميل
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 8px 15px;
                border: none;
                border-radius: 5px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        refresh_btn.clicked.connect(self.load_invoices)
        filter_layout.addWidget(refresh_btn)
        
        filter_layout.addStretch()
        layout.addWidget(filter_frame)
        
        # جدول الفواتير
        self.invoices_table = QTableWidget()
        self.invoices_table.setStyleSheet("""
            QTableWidget {
                font-size: 16px;
                font-weight: bold;
                gridline-color: #D3D3D3;
                background-color: white;
                alternate-background-color: #F8F9FA;
                selection-background-color: #0078D4;
                selection-color: white;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #E0E0E0;
            }
            QTableWidget::item:selected {
                background-color: #0078D4;
                color: white;
            }
            QHeaderView::section {
                background-color: #2C3E50;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 12px;
                border: none;
            }
        """)
        
        self.invoices_table.setColumnCount(8)
        self.invoices_table.setHorizontalHeaderLabels([
            "رقم الفاتورة", "التاريخ", "العميل", "الإجمالي", "المدفوع", "المتبقي", "الحالة", "الإجراءات"
        ])
        
        # تحسين عرض الجدول
        header = self.invoices_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)    # رقم الفاتورة
        header.setSectionResizeMode(1, QHeaderView.Fixed)    # التاريخ
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # العميل
        header.setSectionResizeMode(3, QHeaderView.Fixed)    # الإجمالي
        header.setSectionResizeMode(4, QHeaderView.Fixed)    # المدفوع
        header.setSectionResizeMode(5, QHeaderView.Fixed)    # المتبقي
        header.setSectionResizeMode(6, QHeaderView.Fixed)    # الحالة
        header.setSectionResizeMode(7, QHeaderView.Fixed)    # الإجراءات
        
        # تحديد عرض الأعمدة
        header.resizeSection(0, 140)  # رقم الفاتورة
        header.resizeSection(1, 140)  # التاريخ
        header.resizeSection(3, 140)  # الإجمالي
        header.resizeSection(4, 140)  # المدفوع
        header.resizeSection(5, 140)  # المتبقي
        header.resizeSection(6, 120)  # الحالة
        header.resizeSection(7, 300)  # الإجراءات - زيادة العرض

        # تعديل ارتفاع الصفوف ليناسب الخط المحسن
        self.invoices_table.verticalHeader().setDefaultSectionSize(90)

        # منع تحرير الخلايا
        self.invoices_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        
        # تحديد نمط التحديد
        self.invoices_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        
        layout.addWidget(self.invoices_table)
        
        # شريط الإحصائيات
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border-radius: 5px;
                padding: 15px;
                margin-top: 10px;
            }
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2C3E50;
            }
        """)
        stats_layout = QHBoxLayout()
        stats_frame.setLayout(stats_layout)
        
        self.total_invoices_label = QLabel("إجمالي الفواتير: 0")
        self.total_amount_label = QLabel("إجمالي المبلغ: 0.00 ريال")
        self.paid_amount_label = QLabel("المدفوع: 0.00 ريال")
        self.remaining_amount_label = QLabel("المتبقي: 0.00 ريال")
        
        stats_layout.addWidget(self.total_invoices_label)
        stats_layout.addStretch()
        stats_layout.addWidget(self.total_amount_label)
        stats_layout.addStretch()
        stats_layout.addWidget(self.paid_amount_label)
        stats_layout.addStretch()
        stats_layout.addWidget(self.remaining_amount_label)
        
        layout.addWidget(stats_frame)

    def load_invoices(self):
        """تحميل جميع الفواتير من قاعدة البيانات"""
        try:
            with Session(self.engine) as session:
                # جلب جميع فواتير المبيعات النهائية فقط (استثناء المسودات/المعلقة)
                invoices = session.query(Transaction).filter(
                    Transaction.type == TransactionType.SALE,
                    Transaction.status != 'draft'  # استثنِ الفواتير المعلقة
                ).order_by(Transaction.id.desc()).all()

                self.invoices_table.setRowCount(len(invoices))

                total_amount = 0
                total_paid = 0

                for row, invoice in enumerate(invoices):
                    # رقم الفاتورة
                    self.invoices_table.setItem(
                        row, 0, QTableWidgetItem(f"{invoice.id:06d}")
                    )

                    # التاريخ
                    date_str = invoice.date.strftime("%Y-%m-%d") if invoice.date else ""
                    self.invoices_table.setItem(
                        row, 1, QTableWidgetItem(date_str)
                    )

                    # العميل
                    customer_name = "عميل نقدي"
                    if invoice.customer_id:
                        customer = session.query(Customer).get(invoice.customer_id)
                        if customer:
                            customer_name = customer.name
                    self.invoices_table.setItem(
                        row, 2, QTableWidgetItem(customer_name)
                    )

                    # الإجمالي
                    total = invoice.total_amount or 0
                    self.invoices_table.setItem(
                        row, 3, QTableWidgetItem(format_number(total))
                    )

                    # المدفوع
                    paid = invoice.paid_amount or 0
                    self.invoices_table.setItem(
                        row, 4, QTableWidgetItem(format_number(paid))
                    )

                    # المتبقي
                    remaining = total - paid
                    self.invoices_table.setItem(
                        row, 5, QTableWidgetItem(format_number(remaining))
                    )

                    # الحالة
                    if remaining <= 0:
                        status = "✅ مدفوعة"
                        status_color = QColor(46, 204, 113)  # أخضر
                    else:
                        status = "⏳ جزئية"
                        status_color = QColor(241, 196, 15)  # أصفر

                    status_item = QTableWidgetItem(status)
                    status_item.setBackground(status_color)
                    status_item.setForeground(QColor(255, 255, 255))
                    self.invoices_table.setItem(
                        row, 6, status_item
                    )

                    # أزرار الإجراءات
                    actions_widget = self.create_actions_widget(invoice.id)
                    self.invoices_table.setCellWidget(
                        row, 7, actions_widget
                    )

                    # تجميع الإحصائيات
                    total_amount += total
                    total_paid += paid

                # تحديث الإحصائيات
                self.update_statistics(len(invoices), total_amount, total_paid)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل الفواتير:\n{str(e)}")

    def create_actions_widget(self, invoice_id):
        """إنشاء أزرار الإجراءات لكل فاتورة"""
        widget = QWidget()
        layout = QHBoxLayout()
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)

        # زر عرض التفاصيل
        view_btn = QPushButton("👁️ تفاصيل")
        view_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                font-size: 12px;
                font-weight: bold;
                padding: 6px 10px;
                border: none;
                border-radius: 4px;
                min-width: 80px;
                min-height: 30px;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
        """)
        view_btn.clicked.connect(lambda: self.view_invoice_details(invoice_id))
        layout.addWidget(view_btn)

        # زر طباعة
        print_btn = QPushButton("🖨️ طباعة")
        print_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                color: white;
                font-size: 12px;
                font-weight: bold;
                padding: 6px 10px;
                border: none;
                border-radius: 4px;
                min-width: 80px;
                min-height: 30px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        print_btn.clicked.connect(lambda: self.print_invoice(invoice_id))
        layout.addWidget(print_btn)

        widget.setLayout(layout)
        return widget

    def filter_invoices(self):
        """تصفية الفواتير حسب المعايير المحددة"""
        invoice_search = self.invoice_search.text().strip()
        customer_search = self.customer_search.text().strip().lower()
        date_from = self.date_from.date().toPyDate()
        date_to = self.date_to.date().toPyDate()

        for row in range(self.invoices_table.rowCount()):
            show_row = True

            # فلترة برقم الفاتورة
            if invoice_search:
                invoice_num = self.invoices_table.item(row, 0).text()
                if invoice_search not in invoice_num:
                    show_row = False

            # فلترة باسم العميل
            if customer_search and show_row:
                customer_name = self.invoices_table.item(row, 2).text().lower()
                if customer_search not in customer_name:
                    show_row = False

            # فلترة بالتاريخ
            if show_row:
                date_str = self.invoices_table.item(row, 1).text()
                if date_str:
                    try:
                        invoice_date = datetime.strptime(date_str, "%Y-%m-%d").date()
                        if not (date_from <= invoice_date <= date_to):
                            show_row = False
                    except:
                        show_row = False

            self.invoices_table.setRowHidden(row, not show_row)

    def update_statistics(self, count, total_amount, total_paid):
        """تحديث إحصائيات الفواتير"""
        remaining = total_amount - total_paid

        self.total_invoices_label.setText(f"إجمالي الفواتير: {count}")
        self.total_amount_label.setText(f"إجمالي المبلغ: {format_currency(total_amount)}")
        self.paid_amount_label.setText(f"المدفوع: {format_currency(total_paid)}")
        self.remaining_amount_label.setText(f"المتبقي: {format_currency(remaining)}")

    def view_invoice_details(self, invoice_id):
        """عرض تفاصيل الفاتورة"""
        try:
            dialog = InvoiceDetailsDialog(self.engine, invoice_id)
            # ربط إشارة تحديث المدفوعات بإعادة تحميل الجدول
            dialog.payment_updated.connect(self.load_invoices)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض تفاصيل الفاتورة:\n{str(e)}")

    def print_invoice(self, invoice_id):
        """طباعة الفاتورة بالنظام المتقدم"""
        try:
            from utils.advanced_invoice_printer import show_advanced_print_dialog
            show_advanced_print_dialog(self.engine, invoice_id, self)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء طباعة الفاتورة:\n{str(e)}")


class InvoiceDetailsDialog(QDialog):
    # إشارة لتحديث الجدول الرئيسي عند إضافة دفعة
    payment_updated = pyqtSignal()

    def __init__(self, engine, invoice_id):
        super().__init__()
        self.engine = engine
        self.invoice_id = invoice_id
        # إعداد النافذة مع خاصية التكبير
        setup_large_dialog(self, f"تفاصيل الفاتورة رقم {invoice_id:06d}", 1200, 800, 1400, 900)

        # تحسين النافذة
        self.setStyleSheet("""
            QDialog {
                background-color: #F8F9FA;
                border-radius: 0px;
            }
        """)

        # إضافة أيقونة للنافذة
        self.setWindowIcon(self.style().standardIcon(self.style().SP_FileDialogDetailedView))

        self.setup_ui()
        self.load_invoice_details()

    def setup_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        self.setLayout(layout)

        # عنوان النافذة
        title_label = QLabel(f"📋 تفاصيل الفاتورة رقم {self.invoice_id:06d}")
        title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #2980B9;
            text-align: center;
            margin-bottom: 20px;
            padding: 20px;
            background-color: #F8F9FA;
            border-radius: 10px;
        """)
        layout.addWidget(title_label)

        # معلومات الفاتورة
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #3498DB;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        info_layout = QGridLayout()
        info_layout.setSpacing(15)
        info_frame.setLayout(info_layout)

        # إنشاء labels لعرض معلومات الفاتورة
        self.invoice_info_labels = {}

        # تحسين عرض المعلومات مع labels أوسع
        label_style = """
            font-size: 14px;
            font-weight: bold;
            color: #2C3E50;
            min-width: 120px;
            padding: 5px;
        """

        value_style = """
            font-size: 14px;
            color: #34495E;
            min-width: 200px;
            padding: 8px;
            background-color: #F8F9FA;
            border: 1px solid #E9ECEF;
            border-radius: 5px;
        """

        # الصف الأول
        date_label = QLabel("📅 التاريخ:")
        date_label.setStyleSheet(label_style)
        info_layout.addWidget(date_label, 0, 0)

        self.invoice_info_labels['date'] = QLabel("-")
        self.invoice_info_labels['date'].setStyleSheet(value_style)
        info_layout.addWidget(self.invoice_info_labels['date'], 0, 1)

        customer_label = QLabel("👤 العميل:")
        customer_label.setStyleSheet(label_style)
        info_layout.addWidget(customer_label, 0, 2)

        self.invoice_info_labels['customer'] = QLabel("-")
        self.invoice_info_labels['customer'].setStyleSheet(value_style)
        info_layout.addWidget(self.invoice_info_labels['customer'], 0, 3)

        # الصف الثاني
        total_label = QLabel("💰 الإجمالي:")
        total_label.setStyleSheet(label_style)
        info_layout.addWidget(total_label, 1, 0)

        self.invoice_info_labels['total'] = QLabel("-")
        self.invoice_info_labels['total'].setStyleSheet(value_style)
        info_layout.addWidget(self.invoice_info_labels['total'], 1, 1)

        paid_label = QLabel("💳 المدفوع:")
        paid_label.setStyleSheet(label_style)
        info_layout.addWidget(paid_label, 1, 2)

        self.invoice_info_labels['paid'] = QLabel("-")
        self.invoice_info_labels['paid'].setStyleSheet(value_style)
        info_layout.addWidget(self.invoice_info_labels['paid'], 1, 3)

        # الصف الثالث
        remaining_label = QLabel("⏳ المتبقي:")
        remaining_label.setStyleSheet(label_style)
        info_layout.addWidget(remaining_label, 2, 0)

        self.invoice_info_labels['remaining'] = QLabel("-")
        self.invoice_info_labels['remaining'].setStyleSheet(value_style)
        info_layout.addWidget(self.invoice_info_labels['remaining'], 2, 1)

        status_label = QLabel("📊 الحالة:")
        status_label.setStyleSheet(label_style)
        info_layout.addWidget(status_label, 2, 2)

        self.invoice_info_labels['status'] = QLabel("-")
        self.invoice_info_labels['status'].setStyleSheet(value_style)
        info_layout.addWidget(self.invoice_info_labels['status'], 2, 3)

        # تحديد نسب الأعمدة لتوزيع أفضل
        info_layout.setColumnStretch(1, 1)
        info_layout.setColumnStretch(3, 1)

        layout.addWidget(info_frame)

        # جدول المنتجات
        products_label = QLabel("📦 منتجات الفاتورة")
        products_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #28A745;
            margin-top: 20px;
            margin-bottom: 10px;
        """)
        layout.addWidget(products_label)

        self.products_table = QTableWidget()
        self.products_table.setColumnCount(5)
        self.products_table.setHorizontalHeaderLabels([
            "📦 المنتج", "🔢 الكمية", "💰 السعر", "💵 الإجمالي", "📏 الوحدة"
        ])

        # تحسين عرض الجدول
        header = self.products_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # المنتج
        header.setSectionResizeMode(1, QHeaderView.Fixed)    # الكمية
        header.setSectionResizeMode(2, QHeaderView.Fixed)    # السعر
        header.setSectionResizeMode(3, QHeaderView.Fixed)    # الإجمالي
        header.setSectionResizeMode(4, QHeaderView.Fixed)    # الوحدة

        # تحديد عرض الأعمدة
        header.resizeSection(1, 100)  # الكمية
        header.resizeSection(2, 120)  # السعر
        header.resizeSection(3, 140)  # الإجمالي
        header.resizeSection(4, 100)  # الوحدة

        self.products_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #DEE2E6;
                border-radius: 5px;
                background-color: white;
                font-size: 14px;
                gridline-color: #E9ECEF;
            }
            QTableWidget::item {
                padding: 12px;
                border-bottom: 1px solid #E9ECEF;
                text-align: center;
            }
            QHeaderView::section {
                background-color: #28A745;
                color: white;
                padding: 15px;
                border: none;
                font-weight: bold;
                font-size: 14px;
                text-align: center;
            }
            QHeaderView::section:hover {
                background-color: #218838;
            }
        """)

        # تحديد ارتفاع الصفوف
        self.products_table.verticalHeader().setDefaultSectionSize(90)
        self.products_table.verticalHeader().setVisible(False)

        # منع تحرير الخلايا
        self.products_table.setEditTriggers(QAbstractItemView.NoEditTriggers)

        layout.addWidget(self.products_table)

        # قسم إضافة دفعة جديدة
        payment_label = QLabel("💳 إضافة دفعة جديدة")
        payment_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #FFC107;
            margin-top: 20px;
            margin-bottom: 10px;
        """)
        layout.addWidget(payment_label)

        payment_frame = QFrame()
        payment_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #FFC107;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        payment_layout = QHBoxLayout()
        payment_layout.setSpacing(15)
        payment_frame.setLayout(payment_layout)

        # تحسين label المبلغ
        amount_label = QLabel("💰 المبلغ:")
        amount_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #FF8F00;
            min-width: 80px;
            padding: 5px;
        """)
        payment_layout.addWidget(amount_label)

        self.new_payment_input = QLineEdit()
        self.new_payment_input.setPlaceholderText("أدخل المبلغ...")
        self.new_payment_input.setStyleSheet("""
            QLineEdit {
                padding: 12px;
                border: 2px solid #FFC107;
                border-radius: 5px;
                font-size: 16px;
                min-width: 250px;
                min-height: 20px;
            }
            QLineEdit:focus {
                border-color: #FF8F00;
                background-color: #FFF8E1;
            }
        """)
        payment_layout.addWidget(self.new_payment_input)

        add_payment_btn = QPushButton("➕ إضافة دفعة")
        add_payment_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 12px 25px;
                border: none;
                border-radius: 5px;
                min-width: 150px;
                min-height: 45px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        add_payment_btn.clicked.connect(self.add_payment)
        payment_layout.addWidget(add_payment_btn)

        # إضافة مساحة مرنة في النهاية
        payment_layout.addStretch()

        layout.addWidget(payment_frame)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)

        print_btn = QPushButton("🖨️ طباعة الفاتورة")
        print_btn.setStyleSheet("""
            QPushButton {
                background-color: #17A2B8;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 12px 25px;
                border: none;
                border-radius: 5px;
                min-width: 180px;
                min-height: 45px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        print_btn.clicked.connect(self.print_invoice)

        close_btn = QPushButton("❌ إغلاق النافذة")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #DC3545;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 12px 25px;
                border: none;
                border-radius: 5px;
                min-width: 160px;
                min-height: 45px;
            }
            QPushButton:hover {
                background-color: #C82333;
            }
        """)
        close_btn.clicked.connect(self.close)

        buttons_layout.addStretch()
        buttons_layout.addWidget(print_btn)
        buttons_layout.addWidget(close_btn)

        layout.addLayout(buttons_layout)

    def load_invoice_details(self):
        """تحميل تفاصيل الفاتورة"""
        try:
            with Session(self.engine) as session:
                # جلب الفاتورة
                invoice = session.query(Transaction).get(self.invoice_id)
                if not invoice:
                    QMessageBox.warning(self, "خطأ", "الفاتورة غير موجودة")
                    return

                # تحديث معلومات الفاتورة
                self.invoice_info_labels["date"].setText(
                    invoice.date.strftime("%Y-%m-%d %H:%M") if invoice.date else "-"
                )

                # العميل
                customer_name = "عميل نقدي"
                if invoice.customer_id:
                    customer = session.query(Customer).get(invoice.customer_id)
                    if customer:
                        customer_name = customer.name
                self.invoice_info_labels["customer"].setText(customer_name)

                # المبالغ
                total = invoice.total_amount or 0
                paid = invoice.paid_amount or 0
                remaining = total - paid

                self.invoice_info_labels["total"].setText(f"{total:,.0f} جنيه")
                self.invoice_info_labels["paid"].setText(f"{paid:,.0f} جنيه")
                self.invoice_info_labels["remaining"].setText(f"{remaining:,.0f} جنيه")

                # الحالة
                status = "✅ مدفوعة بالكامل" if remaining <= 0 else "⏳ دفع جزئي"
                self.invoice_info_labels["status"].setText(status)

                # تحميل المنتجات
                self.products_table.setRowCount(len(invoice.items))

                for row, item in enumerate(invoice.items):
                    # المنتج
                    product = session.query(Product).get(item.product_id)
                    product_name = product.name if product else f"منتج رقم {item.product_id}"
                    self.products_table.setItem(row, 0, QTableWidgetItem(product_name))

                    # الكمية
                    self.products_table.setItem(row, 1, QTableWidgetItem(str(item.quantity)))

                    # السعر
                    self.products_table.setItem(row, 2, QTableWidgetItem(f"{item.price:,.0f}"))

                    # الإجمالي
                    item_total = item.quantity * item.price
                    self.products_table.setItem(row, 3, QTableWidgetItem(f"{item_total:,.0f}"))

                    # الوحدة
                    unit = product.unit if product else "قطعة"
                    self.products_table.setItem(row, 4, QTableWidgetItem(unit))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل تفاصيل الفاتورة:\n{str(e)}")

    def add_payment(self):
        """إضافة دفعة جديدة للفاتورة"""
        try:
            # التحقق من صحة المبلغ المدخل
            new_payment_text = self.new_payment_input.text().strip()
            if not new_payment_text:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ الدفعة الجديدة")
                return

            try:
                new_payment = float(new_payment_text)
                if new_payment <= 0:
                    QMessageBox.warning(self, "تحذير", "يجب أن يكون المبلغ أكبر من صفر")
                    return
            except ValueError:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح")
                return

            # جلب بيانات الفاتورة الحالية
            with Session(self.engine) as session:
                invoice = session.query(Transaction).get(self.invoice_id)
                if not invoice:
                    QMessageBox.warning(self, "خطأ", "الفاتورة غير موجودة")
                    return

                current_paid = invoice.paid_amount or 0
                total_amount = invoice.total_amount or 0
                remaining = total_amount - current_paid

                # التحقق من عدم تجاوز المبلغ المطلوب
                if new_payment > remaining:
                    reply = QMessageBox.question(
                        self,
                        "تأكيد",
                        f"المبلغ المدخل ({new_payment:,.2f}) أكبر من المتبقي ({remaining:,.2f})\n"
                        f"هل تريد دفع المتبقي فقط ({remaining:,.2f}) ريال؟",
                        QMessageBox.Yes | QMessageBox.No
                    )
                    if reply == QMessageBox.Yes:
                        new_payment = remaining
                    else:
                        return

                # تأكيد العملية
                new_total_paid = current_paid + new_payment
                new_remaining = total_amount - new_total_paid

                reply = QMessageBox.question(
                    self,
                    "تأكيد إضافة الدفعة",
                    f"تفاصيل الدفعة الجديدة:\n\n"
                    f"💰 المبلغ الحالي المدفوع: {current_paid:,.2f} ريال\n"
                    f"➕ الدفعة الجديدة: {new_payment:,.2f} ريال\n"
                    f"💵 إجمالي المدفوع: {new_total_paid:,.2f} ريال\n"
                    f"⏳ المتبقي: {new_remaining:,.2f} ريال\n\n"
                    f"هل تريد تأكيد هذه العملية؟",
                    QMessageBox.Yes | QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    # تحديث المبلغ المدفوع في قاعدة البيانات
                    invoice.paid_amount = new_total_paid
                    invoice.updated_at = datetime.now()

                    # تحديث رصيد العميل إذا كان موجود
                    if invoice.customer_id:
                        customer = session.query(Customer).get(invoice.customer_id)
                        if customer:
                            customer.balance = customer.balance - new_payment
                            customer.updated_at = datetime.now()

                    session.commit()

                    # إعادة تحميل البيانات وتحديث العرض
                    self.load_invoice_details()
                    self.new_payment_input.clear()

                    # إرسال إشارة لتحديث الجدول الرئيسي
                    self.payment_updated.emit()

                    # رسالة نجاح
                    status_msg = "✅ مدفوعة بالكامل" if new_remaining <= 0 else "⏳ دفع جزئي"
                    QMessageBox.information(
                        self,
                        "تم بنجاح",
                        f"تم إضافة الدفعة بنجاح!\n\n"
                        f"💳 المبلغ المضاف: {new_payment:,.2f} ريال\n"
                        f"💵 إجمالي المدفوع: {new_total_paid:,.2f} ريال\n"
                        f"📊 حالة الفاتورة: {status_msg}"
                    )

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة الدفعة:\n{str(e)}")

    def print_invoice(self):
        """طباعة تفاصيل الفاتورة"""
        try:
            # يمكن إضافة وظيفة الطباعة هنا لاحقاً
            QMessageBox.information(self, "طباعة", f"سيتم طباعة تفاصيل الفاتورة رقم {self.invoice_id:06d}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء طباعة الفاتورة:\n{str(e)}")
