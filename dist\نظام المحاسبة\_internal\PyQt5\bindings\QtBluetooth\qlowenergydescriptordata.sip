// qlowenergydescriptordata.sip generated by MetaSIP
//
// This file is part of the QtBluetooth Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_7_0 -)

class QLowEnergyDescriptorData
{
%TypeHeaderCode
#include <qlowenergydescriptordata.h>
%End

public:
    QLowEnergyDescriptorData();
    QLowEnergyDescriptorData(const QBluetoothUuid &uuid, const QByteArray &value);
    QLowEnergyDescriptorData(const QLowEnergyDescriptorData &other);
    ~QLowEnergyDescriptorData();
    QByteArray value() const;
    void setValue(const QByteArray &value);
    QBluetoothUuid uuid() const;
    void setUuid(const QBluetoothUuid &uuid);
    bool isValid() const;
    void setReadPermissions(bool readable, QBluetooth::AttAccessConstraints constraints = QBluetooth::AttAccessConstraints());
    bool isReadable() const;
    QBluetooth::AttAccessConstraints readConstraints() const;
    void setWritePermissions(bool writable, QBluetooth::AttAccessConstraints constraints = QBluetooth::AttAccessConstraints());
    bool isWritable() const;
    QBluetooth::AttAccessConstraints writeConstraints() const;
    void swap(QLowEnergyDescriptorData &other);
};

%End
%If (Qt_5_7_0 -)
bool operator==(const QLowEnergyDescriptorData &d1, const QLowEnergyDescriptorData &d12);
%End
%If (Qt_5_7_0 -)
bool operator!=(const QLowEnergyDescriptorData &d1, const QLowEnergyDescriptorData &d2);
%End
