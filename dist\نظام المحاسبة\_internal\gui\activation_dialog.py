from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QLineEdit, QMessageBox, QFrame)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
import json
import os
from datetime import datetime
from PyQt5.QtWidgets import QShortcut
from PyQt5.QtGui import QKeySequence
from license_manager import LicenseManager

class ActivationDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.license_manager = LicenseManager()
        self.setup_ui()
        self.load_current_status()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🔑 تفعيل البرنامج")
        self.setFixedSize(900, 1200)  # تكبير الطول بنسبة 100%
        self.setModal(True)

        # العنوان في المنتصف فقط
        title_label = QLabel("🔑 تفعيل البرنامج")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: white;
            margin-bottom: 20px;
        """)
        title_bar = QHBoxLayout()
        title_bar.setContentsMargins(10, 10, 10, 0)
        title_bar.addWidget(title_label)

        # تطبيق التصميم
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #141E30, stop:1 #243B55);
                border-radius: 15px;
            }
            QFrame {
                background-color: rgba(255, 255, 255, 0.95);
                border-radius: 12px;
                padding: 20px;
            }
            QLabel {
                color: #2c3e50;
                font-size: 16px;
            }
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 10px;
                font-size: 16px;
                background: white;
                min-height: 20px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2980b9, stop:1 #1f618d);
            }
            QPushButton:pressed {
                background: #1f618d;
            }
        """)

        main_layout = QVBoxLayout(self)
        main_layout.addLayout(title_bar)

        layout = QVBoxLayout()
        layout.setSpacing(20)
        layout.setContentsMargins(30, 10, 30, 30)

        # بيانات كود العميل واسم الجهاز (ظاهرة دائماً)
        customer_code = self.license_manager.get_customer_code()
        machine_id = self.license_manager.get_machine_id()
        info_box = QFrame()
        info_box.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border: 2px solid #2980b9;
                border-radius: 10px;
                padding: 18px;
                margin-bottom: 10px;
            }
        """)
        info_layout = QVBoxLayout(info_box)
        info_layout.setSpacing(8)
        # صف كود العميل مع زر نسخ
        code_row = QHBoxLayout()
        code_label = QLabel(
            "🔑 كود العميل: "
            f"<span style='color:#1a237e;font-weight:bold'>"
            f"{customer_code}" "</span>"
        )
        code_label.setTextFormat(Qt.RichText)
        code_label.setStyleSheet(
            "font-size: 18px; color: #1a237e; font-weight: bold;"
        )
        code_row.addWidget(code_label)
        copy_code_btn = QPushButton("📋")
        copy_code_btn.setFixedSize(32, 32)
        copy_code_btn.setToolTip("نسخ كود العميل")
        copy_code_btn.clicked.connect(lambda: self.copy_single_value(customer_code, 'كود العميل'))
        code_row.addWidget(copy_code_btn)
        code_row.addStretch()
        info_layout.addLayout(code_row)
        # صف اسم الجهاز مع زر نسخ
        machine_row = QHBoxLayout()
        machine_label = QLabel(
            "💻 اسم الجهاز: "
            f"<span style='color:#00695c;font-weight:bold'>"
            f"{machine_id}" "</span>"
        )
        machine_label.setTextFormat(Qt.RichText)
        machine_label.setStyleSheet(
            "font-size: 18px; color: #00695c; font-weight: bold;"
        )
        machine_row.addWidget(machine_label)
        copy_machine_btn = QPushButton("📋")
        copy_machine_btn.setFixedSize(32, 32)
        copy_machine_btn.setToolTip("نسخ اسم الجهاز")
        copy_machine_btn.clicked.connect(lambda: self.copy_single_value(machine_id, 'اسم الجهاز'))
        machine_row.addWidget(copy_machine_btn)
        machine_row.addStretch()
        info_layout.addLayout(machine_row)
        layout.addWidget(info_box)

        # إطار المحتوى
        content_frame = QFrame()
        content_layout = QVBoxLayout(content_frame)

        # حالة التفعيل الحالية
        self.status_label = QLabel()
        self.status_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
        """)
        content_layout.addWidget(self.status_label)

        # حقل كود التفعيل
        activation_layout = QHBoxLayout()
        activation_layout.addWidget(QLabel("كود التفعيل:"))
        self.activation_code = QLineEdit()
        self.activation_code.setPlaceholderText("أدخل كود التفعيل هنا...")
        activation_layout.addWidget(self.activation_code)
        content_layout.addLayout(activation_layout)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        activate_btn = QPushButton("✅ تفعيل")
        activate_btn.clicked.connect(self.activate_license)
        buttons_layout.addWidget(activate_btn)
        content_layout.addLayout(buttons_layout)
        layout.addWidget(content_frame)

        main_layout.addLayout(layout)
        self.setLayout(main_layout)

        # إضافة اختصار لوحة المفاتيح للشاشة الكاملة (F11)
        self.fullscreen_shortcut = QShortcut(QKeySequence("F11"), self)
        self.fullscreen_shortcut.activated.connect(self.toggle_fullscreen)
        
    def load_current_status(self):
        """تحميل حالة التفعيل الحالية"""
        try:
            settings_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), "company_settings.json")
            if os.path.exists(settings_file):
                with open(settings_file, "r", encoding="utf-8") as f:
                    settings = json.load(f)
                    
                if settings.get("activation_code"):
                    self.status_label.setText("✅ البرنامج مفعل")
                    self.status_label.setStyleSheet(
                        "font-size: 18px; "
                        "font-weight: bold; "
                        "color: #27ae60; "
                        "margin-bottom: 20px;"
                    )
                else:
                    self.status_label.setText("⚠️ البرنامج غير مفعل")
                    self.status_label.setStyleSheet(
                        "font-size: 18px; "
                        "font-weight: bold; "
                        "color: #e74c3c; "
                        "margin-bottom: 20px;"
                    )
                    # إظهار معلومات الجهاز وكود العميل
                    customer_code = self.license_manager.get_customer_code()
                    machine_id = self.license_manager.get_machine_id()
                    self.status_label.setText(
                        "<b>البرنامج غير مفعل!<br>"
                        "يرجى إرسال كود العميل واسم الجهاز للمطور لتفعيل البرنامج.<br><br>"
                        f"🔑 كود العميل: <span style='color:#2980b9'>{customer_code}</span><br>"
                        f"💻 اسم الجهاز: <span style='color:#2980b9'>{machine_id}</span>"
                        "</b>"
                    )
            else:
                self.status_label.setText("⚠️ البرنامج غير مفعل")
        except Exception:
            self.status_label.setText("⚠️ خطأ في تحميل حالة التفعيل")
            
    def activate_license(self):
        """تفعيل البرنامج"""
        activation_code = self.activation_code.text().strip()
        if not activation_code:
            QMessageBox.warning(self, "تنبيه", "يرجى إدخال كود التفعيل")
            return
        # تحقق من الكود باستخدام LicenseManager
        validation = self.license_manager.validate_renewal_code(activation_code)
        if not validation["valid"]:
            QMessageBox.critical(self, "كود غير صحيح", validation["message"])
            return
        try:
            settings_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), "company_settings.json")
            # قراءة الإعدادات الحالية
            if os.path.exists(settings_file):
                with open(settings_file, "r", encoding="utf-8") as f:
                    settings = json.load(f)
            else:
                settings = {}
            # تحديث كود التفعيل وتاريخ الانتهاء
            settings["activation_code"] = activation_code
            settings["activation_date"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            settings["license_expiry"] = validation.get("new_expiry").strftime("%Y-%m-%d") if validation.get("new_expiry") else None
            # حفظ الإعدادات
            with open(settings_file, "w", encoding="utf-8") as f:
                json.dump(settings, f, ensure_ascii=False, indent=4)
            # حساب مدة التفعيل
            expiry = validation.get("new_expiry")
            if expiry:
                days = (expiry - datetime.now()).days
                expiry_str = expiry.strftime("%Y-%m-%d")
                msg = (
                    f"تم تفعيل البرنامج بنجاح!\n"
                    f"مدة التفعيل: {days} يوم (حتى: {expiry_str})"
                )
            else:
                msg = "تم تفعيل البرنامج بنجاح!"
            QMessageBox.information(self, "تم التفعيل", msg)
            self.load_current_status()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء التفعيل: {str(e)}")

    def copy_single_value(self, value, label):
        import pyperclip
        pyperclip.copy(value)
        QMessageBox.information(self, "تم النسخ", f"تم نسخ {label}: {value}")

    def toggle_fullscreen(self):
        """تبديل وضع الشاشة الكاملة"""
        if self.isFullScreen():
            self.showNormal()
            self.fullscreen_btn.setText("🔳")
            self.fullscreen_btn.setToolTip("فتح في شاشة كاملة (F11)")
        else:
            self.showFullScreen()
            self.fullscreen_btn.setText("🗗")
            self.fullscreen_btn.setToolTip("العودة للحجم العادي (F11)") 