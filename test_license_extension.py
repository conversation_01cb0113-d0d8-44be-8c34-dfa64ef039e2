#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إضافة المدة على الترخيص الموجود
"""

import sys
import os
from datetime import datetime, timedelta
import hashlib

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_license_extension():
    """اختبار إضافة مدة على ترخيص موجود"""
    print("🔄 اختبار إضافة مدة على ترخيص موجود")
    print("=" * 50)
    
    try:
        from license_manager import LicenseManager
        
        lm = LicenseManager()
        customer_code = lm.get_customer_code()
        machine_id = lm.get_machine_id()
        
        print(f"🔑 كود العميل: {customer_code}")
        print(f"💻 رقم الجهاز: {machine_id}")
        
        # فحص الترخيص الحالي
        current_status = lm.check_license()
        print(f"\n📊 حالة الترخيص الحالية:")
        print(f"   - صالح: {current_status['valid']}")
        
        if current_status["valid"]:
            print(f"   - ينتهي في: {current_status['expiry_date'].strftime('%d/%m/%Y')}")
            print(f"   - متبقي: {current_status['days_remaining']} يوم")
            
            # محاكاة دالة التحقق من الكود مع إضافة المدة
            def validate_with_extension(license_code, customer_code, machine_id):
                """محاكاة التحقق من الكود مع إضافة المدة"""
                
                # التحقق من تنسيق الكود
                parts = license_code.strip().upper().split('-')
                if len(parts) != 5 or parts[0] != "SICOO":
                    return {"valid": False, "message": "تنسيق كود الترخيص غير صحيح"}
                
                date_str = parts[1]
                try:
                    new_expiry_date = datetime.strptime(date_str, "%Y%m%d")
                    current_date = datetime.now()
                    
                    if current_date > new_expiry_date:
                        return {"valid": False, "message": "كود الترخيص منتهي الصلاحية"}
                    
                    new_days = (new_expiry_date - current_date).days
                    
                except ValueError:
                    return {"valid": False, "message": "تاريخ انتهاء الترخيص غير صحيح"}
                
                # التحقق من الـ hash
                data_to_hash = f"{customer_code}{machine_id}{date_str}SICOO_LICENSE_2024"
                expected_hash = hashlib.sha256(data_to_hash.encode()).hexdigest()[:8].upper()
                
                if parts[4] != expected_hash:
                    return {"valid": False, "message": "كود الترخيص غير صحيح"}
                
                # حساب المدة الإضافية
                current_license = lm._load_license()
                final_expiry_date = new_expiry_date
                license_type = "FULL"
                is_extension = False
                
                if current_license and current_license.get("expiry_date"):
                    try:
                        current_expiry = datetime.fromisoformat(current_license["expiry_date"])
                        
                        if current_expiry > current_date:
                            # إضافة المدة على الترخيص الموجود
                            final_expiry_date = current_expiry + (new_expiry_date - current_date)
                            license_type = "EXTENDED"
                            is_extension = True
                            
                            print(f"\n🔄 حساب المدة الإضافية:")
                            print(f"   - الترخيص الحالي ينتهي: {current_expiry.strftime('%d/%m/%Y')}")
                            print(f"   - مدة الكود الجديد: {new_days} يوم")
                            print(f"   - التاريخ النهائي: {final_expiry_date.strftime('%d/%m/%Y')}")
                            
                    except Exception as e:
                        print(f"خطأ في حساب المدة: {e}")
                
                total_days = (final_expiry_date - current_date).days
                
                license_data = {
                    "customer_code": customer_code,
                    "machine_id": machine_id,
                    "expiry_date": final_expiry_date.isoformat(),
                    "license_type": license_type,
                    "license_code": license_code,
                    "activated_date": datetime.now().isoformat()
                }
                
                return {
                    "valid": True,
                    "message": "كود الترخيص صحيح",
                    "expiry_date": final_expiry_date,
                    "days_remaining": total_days,
                    "license_data": license_data,
                    "is_extension": is_extension
                }
            
            # إنشاء كود جديد للاختبار (365 يوم إضافية)
            new_expiry = datetime.now() + timedelta(days=365)
            date_str = new_expiry.strftime("%Y%m%d")
            data_to_hash = f"{customer_code}{machine_id}{date_str}SICOO_LICENSE_2024"
            hash_value = hashlib.sha256(data_to_hash.encode()).hexdigest()[:8].upper()
            test_code = f"SICOO-{date_str}-{customer_code[:6]}-{machine_id[:6]}-{hash_value}"
            
            print(f"\n📝 كود اختبار جديد: {test_code}")
            print(f"   - مدة الكود: 365 يوم")
            
            # اختبار التحقق من الكود
            result = validate_with_extension(test_code, customer_code, machine_id)
            
            if result["valid"]:
                if result["is_extension"]:
                    print(f"\n✅ تم حساب المدة الإضافية:")
                    print(f"   - المدة الحالية: {current_status['days_remaining']} يوم")
                    print(f"   - المدة الإضافية: 365 يوم")
                    print(f"   - إجمالي المدة: {result['days_remaining']} يوم")
                    print(f"   - التاريخ النهائي: {result['expiry_date'].strftime('%d/%m/%Y')}")
                    print(f"   - نوع العملية: إضافة مدة على ترخيص موجود ✅")
                else:
                    print(f"\n✅ تم استبدال الترخيص:")
                    print(f"   - المدة الجديدة: {result['days_remaining']} يوم")
                    print(f"   - نوع العملية: استبدال ترخيص منتهي")
                
                return True
            else:
                print(f"❌ فشل التحقق: {result['message']}")
                return False
        else:
            print("⚠️ لا يوجد ترخيص صالح حالياً - سيتم إنشاء ترخيص جديد")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_expired_license_replacement():
    """اختبار استبدال ترخيص منتهي"""
    print(f"\n🔄 اختبار استبدال ترخيص منتهي")
    print("=" * 40)
    
    try:
        from license_manager import LicenseManager
        
        lm = LicenseManager()
        customer_code = lm.get_customer_code()
        machine_id = lm.get_machine_id()
        
        # إنشاء ترخيص منتهي للاختبار
        expired_date = datetime.now() - timedelta(days=30)  # منتهي منذ 30 يوم
        expired_license = {
            "customer_code": customer_code,
            "machine_id": machine_id,
            "expiry_date": expired_date.isoformat(),
            "license_type": "EXPIRED_TEST",
            "activated_date": (datetime.now() - timedelta(days=395)).isoformat()
        }
        
        # حفظ الترخيص المنتهي
        lm._save_license(expired_license)
        print("📝 تم إنشاء ترخيص منتهي للاختبار")
        
        # فحص الترخيص
        status = lm.check_license()
        print(f"   - صالح: {status['valid']}")
        print(f"   - انتهى في: {status['expiry_date'].strftime('%d/%m/%Y')}")
        
        if not status["valid"]:
            print("✅ الترخيص منتهي كما متوقع")
            
            # إنشاء كود جديد
            new_expiry = datetime.now() + timedelta(days=365)
            date_str = new_expiry.strftime("%Y%m%d")
            data_to_hash = f"{customer_code}{machine_id}{date_str}SICOO_LICENSE_2024"
            hash_value = hashlib.sha256(data_to_hash.encode()).hexdigest()[:8].upper()
            new_code = f"SICOO-{date_str}-{customer_code[:6]}-{machine_id[:6]}-{hash_value}"
            
            print(f"\n📝 كود جديد للتجديد: {new_code}")
            
            # محاكاة التفعيل
            new_license = {
                "customer_code": customer_code,
                "machine_id": machine_id,
                "expiry_date": new_expiry.isoformat(),
                "license_type": "RENEWED",
                "license_code": new_code,
                "activated_date": datetime.now().isoformat()
            }
            
            lm._save_license(new_license)
            
            # فحص الترخيص الجديد
            new_status = lm.check_license()
            print(f"\n✅ بعد التجديد:")
            print(f"   - صالح: {new_status['valid']}")
            print(f"   - ينتهي في: {new_status['expiry_date'].strftime('%d/%m/%Y')}")
            print(f"   - متبقي: {new_status['days_remaining']} يوم")
            print(f"   - نوع العملية: استبدال ترخيص منتهي ✅")
            
            return new_status["valid"]
        else:
            print("❌ الترخيص لم ينته كما متوقع")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الاستبدال: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔄 اختبار نظام إضافة/استبدال مدة الترخيص")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 2
    
    # اختبار 1: إضافة مدة على ترخيص موجود
    if test_license_extension():
        tests_passed += 1
        print("✅ اختبار إضافة المدة نجح")
    else:
        print("❌ اختبار إضافة المدة فشل")
    
    # اختبار 2: استبدال ترخيص منتهي
    if test_expired_license_replacement():
        tests_passed += 1
        print("✅ اختبار استبدال الترخيص المنتهي نجح")
    else:
        print("❌ اختبار استبدال الترخيص المنتهي فشل")
    
    # النتيجة النهائية
    print(f"\n" + "=" * 60)
    print(f"📊 النتيجة النهائية: {tests_passed}/{total_tests} اختبارات نجحت")
    
    if tests_passed == total_tests:
        print("🎉 جميع الاختبارات نجحت!")
        print("\n📋 خلاصة النظام:")
        print("   ✅ إذا كان الترخيص صالح → إضافة المدة الجديدة عليه")
        print("   ✅ إذا كان الترخيص منتهي → استبدال بترخيص جديد")
        print("   ✅ رسائل واضحة للمستخدم حسب نوع العملية")
    else:
        print("⚠️ بعض الاختبارات فشلت")
    
    return tests_passed == total_tests

if __name__ == "__main__":
    main()
