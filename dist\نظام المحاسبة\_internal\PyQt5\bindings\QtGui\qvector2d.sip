// qvector2d.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%ModuleCode
#include <qvector2d.h>
%End

class QVector2D
{
%TypeHeaderCode
#include <qvector2d.h>
%End

%PickleCode
    sipRes = Py_BuildValue((char *)"dd", (double)sipCpp->x(), (double)sipCpp->y());
%End

public:
    QVector2D();
    QVector2D(float xpos, float ypos);
    explicit QVector2D(const QPoint &point);
    explicit QVector2D(const QPointF &point);
    explicit QVector2D(const QVector3D &vector);
    explicit QVector2D(const QVector4D &vector);
    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        PyObject *x = PyFloat_FromDouble(sipCpp->x());
        PyObject *y = PyFloat_FromDouble(sipCpp->y());
        
        if (x && y)
        {
        #if PY_MAJOR_VERSION >= 3
            sipRes = PyUnicode_FromFormat("PyQt5.QtGui.QVector2D(%R, %R)", x, y);
        #else
            sipRes = PyString_FromString("PyQt5.QtGui.QVector2D(");
            PyString_ConcatAndDel(&sipRes, PyObject_Repr(x));
            PyString_ConcatAndDel(&sipRes, PyString_FromString(", "));
            PyString_ConcatAndDel(&sipRes, PyObject_Repr(y));
            PyString_ConcatAndDel(&sipRes, PyString_FromString(")"));
        #endif
        }
        
        Py_XDECREF(x);
        Py_XDECREF(y);
%End

    float length() const;
    float lengthSquared() const;
    QVector2D normalized() const;
    void normalize();
    static float dotProduct(const QVector2D &v1, const QVector2D &v2);
    QVector3D toVector3D() const;
    QVector4D toVector4D() const;
    bool isNull() const;
    float x() const;
    float y() const;
    void setX(float aX);
    void setY(float aY);
    QVector2D &operator+=(const QVector2D &vector);
    QVector2D &operator-=(const QVector2D &vector);
    QVector2D &operator*=(float factor);
    QVector2D &operator*=(const QVector2D &vector);
    QVector2D &operator/=(float divisor);
%If (Qt_5_5_0 -)
    QVector2D &operator/=(const QVector2D &vector);
%End
    QPoint toPoint() const;
    QPointF toPointF() const;
%If (Qt_5_1_0 -)
    float distanceToPoint(const QVector2D &point) const;
%End
%If (Qt_5_1_0 -)
    float distanceToLine(const QVector2D &point, const QVector2D &direction) const;
%End
%If (Qt_5_2_0 -)
    float operator[](int i) const;
%End
};

bool operator==(const QVector2D &v1, const QVector2D &v2);
bool operator!=(const QVector2D &v1, const QVector2D &v2);
const QVector2D operator+(const QVector2D &v1, const QVector2D &v2);
const QVector2D operator-(const QVector2D &v1, const QVector2D &v2);
const QVector2D operator*(float factor, const QVector2D &vector);
const QVector2D operator*(const QVector2D &vector, float factor);
const QVector2D operator*(const QVector2D &v1, const QVector2D &v2);
const QVector2D operator-(const QVector2D &vector);
const QVector2D operator/(const QVector2D &vector, float divisor);
%If (Qt_5_5_0 -)
const QVector2D operator/(const QVector2D &vector, const QVector2D &divisor);
%End
bool qFuzzyCompare(const QVector2D &v1, const QVector2D &v2);
QDataStream &operator<<(QDataStream &, const QVector2D & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QVector2D & /Constrained/) /ReleaseGIL/;
