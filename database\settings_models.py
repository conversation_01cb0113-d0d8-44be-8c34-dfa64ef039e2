#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نماذج قاعدة البيانات للإعدادات
"""

from sqlalchemy import Column, Integer, String, Boolean, Text, DateTime
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime

Base = declarative_base()

class AppSettings(Base):
    """جدول إعدادات التطبيق"""
    __tablename__ = 'app_settings'
    
    id = Column(Integer, primary_key=True)
    setting_key = Column(String(100), unique=True, nullable=False)
    setting_value = Column(Text)
    setting_type = Column(String(50))  # string, boolean, integer, json
    description = Column(String(500))
    category = Column(String(100))  # appearance, language, currency, shortcuts
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f"<AppSettings(key='{self.setting_key}', value='{self.setting_value}')>"

# الإعدادات الافتراضية
DEFAULT_SETTINGS = {
    # إعدادات اللغة
    'language': {
        'value': 'ar',
        'type': 'string',
        'description': 'لغة واجهة التطبيق',
        'category': 'language'
    },
    
    # إعدادات المظهر
    'theme': {
        'value': 'light',
        'type': 'string', 
        'description': 'مظهر التطبيق (فاتح/داكن)',
        'category': 'appearance'
    },
    'font_size': {
        'value': '24',
        'type': 'integer',
        'description': 'حجم الخط الافتراضي',
        'category': 'appearance'
    },
    'window_maximized': {
        'value': 'true',
        'type': 'boolean',
        'description': 'فتح النوافذ بحجم كامل',
        'category': 'appearance'
    },
    
    # إعدادات العملة
    'currency': {
        'value': 'EGP',
        'type': 'string',
        'description': 'العملة الافتراضية',
        'category': 'currency'
    },
    'currency_symbol': {
        'value': 'ج.م',
        'type': 'string',
        'description': 'رمز العملة',
        'category': 'currency'
    },
    'currency_position': {
        'value': 'after',
        'type': 'string',
        'description': 'موضع رمز العملة (قبل/بعد)',
        'category': 'currency'
    },
    'decimal_places': {
        'value': '0',
        'type': 'integer',
        'description': 'عدد الخانات العشرية',
        'category': 'currency'
    },
    
    # إعدادات الاختصارات
    'profit_toggle_shortcut': {
        'value': 'Ctrl+T',
        'type': 'string',
        'description': 'اختصار إظهار/إخفاء المكسب',
        'category': 'shortcuts'
    },
    'new_invoice_shortcut': {
        'value': 'Ctrl+N',
        'type': 'string',
        'description': 'اختصار فاتورة جديدة',
        'category': 'shortcuts'
    },
    'save_shortcut': {
        'value': 'Ctrl+S',
        'type': 'string',
        'description': 'اختصار الحفظ',
        'category': 'shortcuts'
    },
    'print_shortcut': {
        'value': 'Ctrl+P',
        'type': 'string',
        'description': 'اختصار الطباعة',
        'category': 'shortcuts'
    },
    'search_shortcut': {
        'value': 'Ctrl+F',
        'type': 'string',
        'description': 'اختصار البحث',
        'category': 'shortcuts'
    },
    
    # إعدادات عامة
    'auto_backup': {
        'value': 'true',
        'type': 'boolean',
        'description': 'النسخ الاحتياطي التلقائي',
        'category': 'general'
    },
    'backup_interval': {
        'value': '7',
        'type': 'integer',
        'description': 'فترة النسخ الاحتياطي (أيام)',
        'category': 'general'
    },
    'show_notifications': {
        'value': 'true',
        'type': 'boolean',
        'description': 'إظهار الإشعارات',
        'category': 'general'
    }
}

# العملات المدعومة
SUPPORTED_CURRENCIES = {
    'EGP': {'name': 'جنيه مصري', 'symbol': 'ج.م', 'code': 'EGP'},
    'SAR': {'name': 'ريال سعودي', 'symbol': 'ر.س', 'code': 'SAR'},
    'USD': {'name': 'دولار أمريكي', 'symbol': '$', 'code': 'USD'},
    'EUR': {'name': 'يورو', 'symbol': '€', 'code': 'EUR'},
    'GBP': {'name': 'جنيه إسترليني', 'symbol': '£', 'code': 'GBP'},
    'AED': {'name': 'درهم إماراتي', 'symbol': 'د.إ', 'code': 'AED'},
    'KWD': {'name': 'دينار كويتي', 'symbol': 'د.ك', 'code': 'KWD'},
    'QAR': {'name': 'ريال قطري', 'symbol': 'ر.ق', 'code': 'QAR'},
    'BHD': {'name': 'دينار بحريني', 'symbol': 'د.ب', 'code': 'BHD'},
    'OMR': {'name': 'ريال عماني', 'symbol': 'ر.ع', 'code': 'OMR'},
    'JOD': {'name': 'دينار أردني', 'symbol': 'د.أ', 'code': 'JOD'},
    'LBP': {'name': 'ليرة لبنانية', 'symbol': 'ل.ل', 'code': 'LBP'},
    'TRY': {'name': 'ليرة تركية', 'symbol': '₺', 'code': 'TRY'},
    'MAD': {'name': 'درهم مغربي', 'symbol': 'د.م', 'code': 'MAD'},
    'DZD': {'name': 'دينار جزائري', 'symbol': 'د.ج', 'code': 'DZD'},
    'TND': {'name': 'دينار تونسي', 'symbol': 'د.ت', 'code': 'TND'},
    'LYD': {'name': 'دينار ليبي', 'symbol': 'د.ل', 'code': 'LYD'},
    'SDG': {'name': 'جنيه سوداني', 'symbol': 'ج.س', 'code': 'SDG'},
    'IQD': {'name': 'دينار عراقي', 'symbol': 'د.ع', 'code': 'IQD'},
    'SYP': {'name': 'ليرة سورية', 'symbol': 'ل.س', 'code': 'SYP'},
    'YER': {'name': 'ريال يمني', 'symbol': 'ر.ي', 'code': 'YER'}
}

# اللغات المدعومة
SUPPORTED_LANGUAGES = {
    'ar': {'name': 'العربية', 'direction': 'rtl'},
    'en': {'name': 'English', 'direction': 'ltr'}
}

# المظاهر المدعومة
SUPPORTED_THEMES = {
    'light': {'name': 'فاتح', 'name_en': 'Light'},
    'dark': {'name': 'داكن', 'name_en': 'Dark'}
}

def init_settings_db(engine):
    """إنشاء جدول الإعدادات وإدراج القيم الافتراضية"""
    from sqlalchemy.orm import Session
    
    # إنشاء الجداول
    Base.metadata.create_all(engine)
    
    # إدراج الإعدادات الافتراضية
    with Session(engine) as session:
        for key, config in DEFAULT_SETTINGS.items():
            # التحقق من وجود الإعداد
            existing = session.query(AppSettings).filter(
                AppSettings.setting_key == key
            ).first()
            
            if not existing:
                setting = AppSettings(
                    setting_key=key,
                    setting_value=config['value'],
                    setting_type=config['type'],
                    description=config['description'],
                    category=config['category']
                )
                session.add(setting)
        
        session.commit()
        print("✅ تم إنشاء جدول الإعدادات وإدراج القيم الافتراضية")

if __name__ == "__main__":
    try:
        from main import resource_path
        db_path = resource_path('accounting.db')
        engine = create_engine(f'sqlite:///{db_path}', echo=True)
    except ImportError:
        engine = create_engine('sqlite:///accounting.db', echo=True)
    
    # اختبار إنشاء الجدول
    init_settings_db(engine)
    print("🎉 تم إنشاء نظام الإعدادات بنجاح!")
