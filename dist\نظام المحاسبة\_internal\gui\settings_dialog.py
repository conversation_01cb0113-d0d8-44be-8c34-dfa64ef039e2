from PyQt5.QtWidgets import (Q<PERSON><PERSON>og, Q<PERSON>oxLayout, QHBox<PERSON><PERSON>out, QW<PERSON>t, QLabel,
                             QComboBox, QPushButton, QCheckBox, QSpinBox, QLineEdit,
                             QGroupBox, QGridLayout, QMessageBox, QFrame, QStackedWidget)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

from utils.settings_manager import get_settings_manager
from utils.dialog_utils import make_dialog_resizable
from utils.theme_manager import theme_manager
from database.settings_models import SUPPORTED_CURRENCIES, SUPPORTED_LANGUAGES, SUPPORTED_THEMES


class SettingsDialog(QDialog):
    settings_changed = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إعدادات التطبيق")
        self.setModal(True)

        # إضافة خاصية التكبير والتصغير
        make_dialog_resizable(self, 1000, 700, 1200, 800)
        
        # تطبيق الإعدادات الحالية
        self.settings_manager = get_settings_manager()
        
        # التحقق من صلاحيات المدير
        self.is_admin = self.check_admin_permission()
        
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        self.setLayout(layout)

        # الهيدر
        header_frame = QFrame()
        header_frame.setFixedHeight(80)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4A90E2, stop:1 #357ABD);
                border: none;
            }
        """)
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(30, 20, 30, 20)
        header_frame.setLayout(header_layout)

        title_label = QLabel("⚙️ إعدادات التطبيق")
        title_label.setStyleSheet("""
            color: white;
            font-size: 24px;
            font-weight: bold;
        """)
        header_layout.addWidget(title_label)
        header_layout.addStretch()

        subtitle_label = QLabel("تخصيص المنتج حسب احتياجاتك")
        subtitle_label.setStyleSheet("""
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
        """)
        header_layout.addWidget(subtitle_label)

        layout.addWidget(header_frame)
        
        # إضافة مساحة فاصلة
        layout.addSpacing(10)

        # نظام التبويبات المخصص
        self.create_custom_tabs(layout)

        # أزرار التحكم
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout()
        buttons_layout.setContentsMargins(20, 15, 20, 15)
        buttons_layout.setSpacing(10)
        buttons_frame.setLayout(buttons_layout)

        # أزرار ملونة
        button_style = """
            QPushButton {
                padding: 12px 25px;
                font-size: 14px;
                font-weight: bold;
                border: none;
                border-radius: 8px;
                min-width: 120px;
            }
        """

        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet(button_style + """
            QPushButton {
                background-color: #95A5A6;
                color: white;
            }
            QPushButton:hover {
                background-color: #7F8C8D;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        save_btn = QPushButton("💾 حفظ")
        save_btn.setStyleSheet(button_style + """
            QPushButton {
                background-color: #27AE60;
                color: white;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        save_btn.clicked.connect(self.save_settings)
        buttons_layout.addWidget(save_btn)

        buttons_layout.addStretch()

        layout.addWidget(buttons_frame)
    
    def create_custom_tabs(self, layout):
        """إنشاء نظام تبويبات مخصص"""
        # إطار التبويبات
        tabs_frame = QFrame()
        tabs_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: none;
                margin: 0px;
                padding: 0px;
            }
        """)
        tabs_layout = QVBoxLayout()
        tabs_layout.setContentsMargins(0, 0, 0, 0)
        tabs_layout.setSpacing(0)
        tabs_frame.setLayout(tabs_layout)
        
        # شريط التبويبات
        self.tabs_bar = QFrame()
        self.tabs_bar.setFixedHeight(50)
        self.tabs_bar.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border-bottom: 2px solid #DEE2E6;
                margin: 0px;
                padding: 0px;
            }
        """)
        tabs_bar_layout = QHBoxLayout()
        tabs_bar_layout.setContentsMargins(10, 5, 10, 5)
        tabs_bar_layout.setSpacing(5)
        self.tabs_bar.setLayout(tabs_bar_layout)
        
        # أزرار التبويبات
        self.tab_buttons = []
        self.current_tab = 0
        
        tab_names = [
            ("🎨 اللغة", "إعدادات اللغة والمظهر"),
            ("💰 العملة", "إعدادات العملة والرموز"),
            ("⌨️ اختصارات", "اختصارات لوحة المفاتيح"),
            ("💾 نسخ احتياطي", "النسخ الاحتياطي والإشعارات")
        ]
        
        # إضافة تبويب إعادة ضبط النظام للمدير فقط
        if self.is_admin:
            tab_names.append(("🔄 إعادة ضبط", "إعادة ضبط النظام (للمدير فقط)"))
        
        for i, (name, tooltip) in enumerate(tab_names):
            btn = QPushButton(name)
            btn.setToolTip(tooltip)
            btn.setCheckable(True)
            btn.setFixedHeight(40)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #E9ECEF;
                    color: #495057;
                    border: 1px solid #DEE2E6;
                    border-bottom: none;
                    border-top-left-radius: 8px;
                    border-top-right-radius: 8px;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 8px 20px;
                    margin-right: 2px;
                    text-align: center;
                }
                QPushButton:checked {
                    background-color: #4A90E2;
                    color: white;
                    border-color: #4A90E2;
                }
                QPushButton:hover:!checked {
                    background-color: #F1F3F4;
                    color: #4A90E2;
                }
            """)
            btn.clicked.connect(lambda checked, index=i: self.switch_tab(index))
            self.tab_buttons.append(btn)
            tabs_bar_layout.addWidget(btn)
        
        tabs_bar_layout.addStretch()
        
        # منطقة المحتوى
        self.content_area = QStackedWidget()
        self.content_area.setStyleSheet("""
            QStackedWidget {
                background-color: white;
                border: 1px solid #DEE2E6;
                border-top: none;
                padding: 20px;
            }
        """)
        
        # إنشاء صفحات التبويبات
        self.create_appearance_page()
        self.create_currency_page()
        self.create_shortcuts_page()
        self.create_general_page()
        
        # إضافة صفحة إعادة ضبط النظام للمدير فقط
        if self.is_admin:
            self.create_reset_page()
        
        # تعيين التبويب الأول كمحدد
        self.tab_buttons[0].setChecked(True)
        self.content_area.setCurrentIndex(0)
        
        # إضافة العناصر للتخطيط
        tabs_layout.addWidget(self.tabs_bar)
        tabs_layout.addWidget(self.content_area)
        
        layout.addWidget(tabs_frame)
    
    def switch_tab(self, index):
        """تبديل التبويب"""
        # إلغاء تحديد جميع الأزرار
        for btn in self.tab_buttons:
            btn.setChecked(False)
        
        # تحديد الزر المطلوب
        self.tab_buttons[index].setChecked(True)
        
        # تبديل المحتوى
        self.content_area.setCurrentIndex(index)
        self.current_tab = index
    
    def create_appearance_page(self):
        """إنشاء صفحة اللغة والمظهر"""
        page = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(25)
        page.setLayout(layout)
        
        # مجموعة اللغة
        language_group = QGroupBox("🌐 إعدادات اللغة")
        language_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #2C3E50;
                border: 2px solid #4A90E2;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: #F8F9FA;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: white;
            }
        """)
        language_layout = QGridLayout()
        language_layout.setContentsMargins(20, 20, 20, 20)
        language_layout.setSpacing(15)
        language_group.setLayout(language_layout)

        lang_label = QLabel("اللغة:")
        lang_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #495057;")
        language_layout.addWidget(lang_label, 0, 0)

        self.language_combo = QComboBox()
        for code, info in SUPPORTED_LANGUAGES.items():
            self.language_combo.addItem(f"{info['name']}", code)
        self.language_combo.setStyleSheet("""
            QComboBox {
                padding: 12px 15px;
                font-size: 16px;
                border: 2px solid #DEE2E6;
                border-radius: 8px;
                background-color: white;
                min-width: 200px;
            }
            QComboBox:focus {
                border-color: #4A90E2;
            }
        """)
        language_layout.addWidget(self.language_combo, 0, 1)

        layout.addWidget(language_group)
        layout.addStretch()

        self.content_area.addWidget(page)

    def create_currency_page(self):
        """إنشاء صفحة العملة"""
        page = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(25)
        page.setLayout(layout)

        # مجموعة العملة
        currency_group = QGroupBox("💰 إعدادات العملة")
        currency_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #2C3E50;
                border: 2px solid #4A90E2;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: #F8F9FA;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: white;
            }
        """)
        currency_layout = QGridLayout()
        currency_layout.setContentsMargins(20, 20, 20, 20)
        currency_layout.setSpacing(15)
        currency_group.setLayout(currency_layout)

        currency_layout.addWidget(QLabel("العملة:"), 0, 0)
        self.currency_combo = QComboBox()
        for code, info in SUPPORTED_CURRENCIES.items():
            self.currency_combo.addItem(f"{info['name']} ({info['symbol']})", code)
        self.currency_combo.setStyleSheet("""
            QComboBox {
                padding: 12px 15px;
                font-size: 16px;
                border: 2px solid #DEE2E6;
                border-radius: 8px;
                background-color: white;
                min-width: 200px;
            }
            QComboBox:focus {
                border-color: #4A90E2;
            }
        """)
        currency_layout.addWidget(self.currency_combo, 0, 1)

        layout.addWidget(currency_group)
        layout.addStretch()

        self.content_area.addWidget(page)

    def create_shortcuts_page(self):
        """إنشاء صفحة الاختصارات"""
        page = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(25)
        page.setLayout(layout)

        # مجموعة الاختصارات
        shortcuts_group = QGroupBox("⌨️ اختصارات لوحة المفاتيح")
        shortcuts_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #2C3E50;
                border: 2px solid #4A90E2;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: #F8F9FA;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: white;
            }
        """)
        shortcuts_layout = QGridLayout()
        shortcuts_layout.setContentsMargins(20, 20, 20, 20)
        shortcuts_layout.setSpacing(15)
        shortcuts_group.setLayout(shortcuts_layout)

        shortcuts_layout.addWidget(QLabel("إظهار/إخفاء المكسب:"), 0, 0)
        self.profit_toggle_edit = QLineEdit()
        self.profit_toggle_edit.setStyleSheet("""
            QLineEdit {
                padding: 12px 15px;
                font-size: 16px;
                border: 2px solid #DEE2E6;
                border-radius: 8px;
                background-color: white;
                min-width: 200px;
            }
            QLineEdit:focus {
                border-color: #4A90E2;
            }
        """)
        shortcuts_layout.addWidget(self.profit_toggle_edit, 0, 1)

        layout.addWidget(shortcuts_group)
        layout.addStretch()

        self.content_area.addWidget(page)

    def create_general_page(self):
        """إنشاء صفحة الإعدادات العامة"""
        page = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(25)
        page.setLayout(layout)

        # مجموعة النسخ الاحتياطي
        backup_group = QGroupBox("💾 النسخ الاحتياطي")
        backup_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #2C3E50;
                border: 2px solid #4A90E2;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: #F8F9FA;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: white;
            }
        """)
        backup_layout = QVBoxLayout()
        backup_layout.setContentsMargins(20, 20, 20, 20)
        backup_layout.setSpacing(15)
        backup_group.setLayout(backup_layout)

        self.auto_backup_checkbox = QCheckBox("تفعيل النسخ الاحتياطي التلقائي")
        self.auto_backup_checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 16px;
                font-weight: bold;
                color: #495057;
                spacing: 10px;
                padding: 10px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border: 2px solid #DEE2E6;
                border-radius: 4px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #4A90E2;
                border-color: #4A90E2;
            }
        """)
        backup_layout.addWidget(self.auto_backup_checkbox)

        layout.addWidget(backup_group)
        layout.addStretch()

        self.content_area.addWidget(page)

    def create_reset_page(self):
        """إنشاء صفحة إعادة ضبط النظام"""
        page = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(25)
        page.setLayout(layout)

        # مجموعة الإعدادات العامة
        general_group = QGroupBox("🔄 إعادة ضبط النظام")
        general_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #2C3E50;
                border: 2px solid #4A90E2;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: #F8F9FA;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: white;
            }
        """)
        general_layout = QVBoxLayout()
        general_layout.setContentsMargins(20, 20, 20, 20)
        general_layout.setSpacing(15)
        general_group.setLayout(general_layout)

        self.reset_button = QPushButton("إعادة ضبط النظام")
        self.reset_button.setStyleSheet("""
            QPushButton {
                padding: 12px 25px;
                font-size: 14px;
                font-weight: bold;
                border: none;
                border-radius: 8px;
                min-width: 120px;
            }
        """)
        self.reset_button.clicked.connect(self.reset_system)
        general_layout.addWidget(self.reset_button)

        layout.addWidget(general_group)
        layout.addStretch()

        self.content_area.addWidget(page)

    def load_current_settings(self):
        """تحميل الإعدادات الحالية"""
        if not self.settings_manager:
            return

        # إعدادات اللغة
        language = self.settings_manager.get('language', 'ar')
        for i in range(self.language_combo.count()):
            if self.language_combo.itemData(i) == language:
                self.language_combo.setCurrentIndex(i)
                break

        # إعدادات العملة
        currency = self.settings_manager.get('currency', 'EGP')
        for i in range(self.currency_combo.count()):
            if self.currency_combo.itemData(i) == currency:
                self.currency_combo.setCurrentIndex(i)
                break

        # إعدادات الاختصارات
        self.profit_toggle_edit.setText(self.settings_manager.get('profit_toggle_shortcut', 'Ctrl+T'))

        # إعدادات عامة
        auto_backup = self.settings_manager.get('auto_backup', True)
        self.auto_backup_checkbox.setChecked(auto_backup)

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # حفظ إعدادات اللغة
            self.settings_manager.set('language', self.language_combo.currentData())

            # حفظ إعدادات العملة
            self.settings_manager.set('currency', self.currency_combo.currentData())

            # حفظ الاختصارات
            self.settings_manager.set('profit_toggle_shortcut', self.profit_toggle_edit.text())

            # حفظ الإعدادات العامة
            self.settings_manager.set('auto_backup', self.auto_backup_checkbox.isChecked())

            # إرسال إشارة تغيير الإعدادات
            self.settings_changed.emit()

            QMessageBox.information(self, "نجح الحفظ", "تم حفظ الإعدادات بنجاح!")
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الإعدادات:\n{str(e)}")

    def show_app_settings(self):
        """عرض نافذة الإعدادات"""
        try:
            self.load_current_settings()
            self.exec_()
        except Exception as e:
            QMessageBox.critical(None, "خطأ", f"Error in show_app_settings: {str(e)}")
            print(f"Error in show_app_settings: {str(e)}")

    def update_theme(self):
        """تحديث ألوان الثيم"""
        # تطبيق الثيم العام
        self.setStyleSheet(theme_manager.get_stylesheet("general"))

        # تحديث الألوان
        colors = theme_manager.get_colors()

        # تحديث الهيدر
        header_style = f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {colors['primary']}, stop:1 {colors['primary_dark']});
                border: none;
            }}
        """

        # تحديث التبويبات
        tab_style = f"""
            QFrame {{
                background-color: {colors['card_bg']};
                border-bottom: 2px solid {colors['border_color']};
                margin: 0px;
                padding: 0px;
            }}
        """

        # تحديث أزرار التبويبات
        button_style = f"""
            QPushButton {{
                background-color: {colors['secondary_bg']};
                color: {colors['primary_text']};
                border: 1px solid {colors['border_color']};
                border-bottom: none;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                padding: 8px 20px;
                margin-right: 2px;
                text-align: center;
            }}
            QPushButton:checked {{
                background-color: {colors['primary']};
                color: white;
                border-color: {colors['primary']};
            }}
            QPushButton:hover:!checked {{
                background-color: {colors['button_hover']};
                color: {colors['primary']};
            }}
        """

        # تحديث القوائم المنسدلة والحقول
        input_style = f"""
            QComboBox, QLineEdit {{
                padding: 12px 15px;
                font-size: 16px;
                border: 2px solid {colors['border_color']};
                border-radius: 8px;
                background-color: {colors['input_bg']};
                color: {colors['input_text']};
                min-width: 200px;
            }}
            QComboBox:focus, QLineEdit:focus {{
                border-color: {colors['input_focus']};
            }}
        """

        # تطبيق الأنماط
        if hasattr(self, 'tabs_bar'):
            self.tabs_bar.setStyleSheet(tab_style)

        if hasattr(self, 'tab_buttons'):
            for btn in self.tab_buttons:
                btn.setStyleSheet(button_style)

        if hasattr(self, 'language_combo'):
            self.language_combo.setStyleSheet(input_style)
        if hasattr(self, 'currency_combo'):
            self.currency_combo.setStyleSheet(input_style)
        if hasattr(self, 'profit_toggle_edit'):
            self.profit_toggle_edit.setStyleSheet(input_style)

    def reset_system(self):
        """إعادة ضبط النظام"""
        from .reset_system_dialog import ResetSystemDialog
        dialog = ResetSystemDialog(self.parent())
        dialog.exec_()

    def check_admin_permission(self):
        """التحقق من صلاحيات المدير"""
        try:
            # محاولة الوصول للنافذة الرئيسية للتحقق من صلاحيات المستخدم
            parent = self.parent()
            if parent and hasattr(parent, 'check_permission'):
                return parent.check_permission("إدارة_المستخدمين")
            else:
                # إذا لم نتمكن من الوصول للصلاحيات، نفترض أن المستخدم مدير
                return True
        except:
            # في حالة حدوث خطأ، نفترض أن المستخدم مدير
            return True
