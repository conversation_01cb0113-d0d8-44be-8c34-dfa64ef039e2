#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص المنتجات الموجودة في قاعدة البيانات لفهم سبب اعتبار جميع المنتجات مكررة
"""

import sys
import os
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.models import Product

def check_current_products():
    """فحص المنتجات الموجودة في قاعدة البيانات"""
    
    try:
        engine = create_engine('sqlite:///accounting.db')
        Session = sessionmaker(bind=engine)
        
        with Session() as session:
            # عد المنتجات الموجودة
            total_products = session.query(Product).count()
            print(f"📊 إجمالي المنتجات في قاعدة البيانات: {total_products}")
            
            if total_products == 0:
                print("❌ لا توجد منتجات في قاعدة البيانات!")
                return
            
            # عرض آخر 10 منتجات
            print("\n📋 آخر 10 منتجات:")
            recent_products = session.query(Product).order_by(Product.id.desc()).limit(10).all()
            for i, product in enumerate(recent_products, 1):
                code_display = product.code if product.code else '[فارغ]'
                print(f"  {i}. {product.name[:50]}... | كود: {code_display}")
            
            # فحص الأكواد المكررة
            print("\n🔍 فحص الأكواد المكررة...")
            duplicates = session.execute(text("""
                SELECT code, COUNT(*) as count 
                FROM products 
                WHERE code IS NOT NULL AND code != ''
                GROUP BY code 
                HAVING COUNT(*) > 1
                ORDER BY count DESC
                LIMIT 10
            """)).fetchall()
            
            if duplicates:
                print(f"⚠️ وجد {len(duplicates)} كود مكرر:")
                for code, count in duplicates:
                    print(f"  - الكود '{code}' مكرر {count} مرة")
            else:
                print("✅ لا توجد أكواد مكررة")
            
            # فحص الأسماء المكررة
            print("\n🔍 فحص الأسماء المكررة...")
            name_duplicates = session.execute(text("""
                SELECT name, COUNT(*) as count 
                FROM products 
                GROUP BY name 
                HAVING COUNT(*) > 1
                ORDER BY count DESC
                LIMIT 10
            """)).fetchall()
            
            if name_duplicates:
                print(f"⚠️ وجد {len(name_duplicates)} اسم مكرر:")
                for name, count in name_duplicates:
                    print(f"  - الاسم '{name[:50]}...' مكرر {count} مرة")
            else:
                print("✅ لا توجد أسماء مكررة")
            
            # فحص المنتجات بدون أكواد
            no_code_count = session.execute(text("""
                SELECT COUNT(*) 
                FROM products 
                WHERE code IS NULL OR code = ''
            """)).scalar()
            
            print(f"\n📊 عدد المنتجات بدون أكواد: {no_code_count}")
            
            # عرض عينة من المنتجات بدون أكواد
            if no_code_count > 0:
                print("\n📋 عينة من المنتجات بدون أكواد:")
                no_code_products = session.execute(text("""
                    SELECT name 
                    FROM products 
                    WHERE code IS NULL OR code = ''
                    LIMIT 5
                """)).fetchall()
                
                for i, (name,) in enumerate(no_code_products, 1):
                    print(f"  {i}. {name[:50]}...")
            
            # إحصائيات عامة
            print(f"\n📊 إحصائيات عامة:")
            
            # عدد المنتجات بأكواد فريدة
            unique_codes = session.execute(text("""
                SELECT COUNT(DISTINCT code) 
                FROM products 
                WHERE code IS NOT NULL AND code != ''
            """)).scalar()
            
            print(f"  - منتجات بأكواد فريدة: {unique_codes}")
            print(f"  - منتجات بدون أكواد: {no_code_count}")
            print(f"  - إجمالي المنتجات: {total_products}")
            
            # فحص إذا كانت هناك منتجات بنفس الأسماء من ملف الاستيراد
            print(f"\n🔍 فحص عينة من أسماء المنتجات الشائعة...")
            common_names = [
                'حامل تورتة اكرلك دهب',
                'فرشة حوض فاست مد استار',
                'فرشة حمام فلورا تركي 392',
                'طقم مصفي 3 ق استانلس ابوالعنين',
                'طقم توزيع استيل الحداد مستطيل ومدور'
            ]
            
            for name in common_names:
                count = session.query(Product).filter(Product.name == name).count()
                if count > 0:
                    print(f"  - '{name[:40]}...': {count} منتج")
                    
                    # عرض تفاصيل المنتجات بهذا الاسم
                    products_with_name = session.query(Product).filter(Product.name == name).all()
                    for product in products_with_name:
                        code_display = product.code if product.code else '[فارغ]'
                        print(f"    → ID: {product.id}, كود: {code_display}")
            
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")

if __name__ == "__main__":
    check_current_products()
