// qocspresponse.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_13_0 -)
%If (PyQt_SSL)
%ModuleCode
#include <qocspresponse.h>
%End
%End
%End

%If (Qt_5_13_0 -)
%If (PyQt_SSL)

enum class QOcspCertificateStatus
{
    Good,
    Revoked,
    Unknown,
};

%End
%End
%If (Qt_5_13_0 -)
%If (PyQt_SSL)

enum class QOcspRevocationReason
{
    None /PyName=None_/,
    Unspecified,
    KeyCompromise,
    CACompromise,
    AffiliationChanged,
    Superseded,
    CessationOfOperation,
    CertificateHold,
    RemoveFromCRL,
};

%End
%End
%If (Qt_5_13_0 -)
%If (PyQt_SSL)

class QOcspResponse
{
%TypeHeaderCode
#include <qocspresponse.h>
%End

public:
    QOcspResponse();
    QOcspResponse(const QOcspResponse &other);
    ~QOcspResponse();
    QOcspCertificateStatus certificateStatus() const;
    QOcspRevocationReason revocationReason() const;
    QSslCertificate responder() const;
    QSslCertificate subject() const;
    void swap(QOcspResponse &other);
    long __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End
};

%End
%End
%If (Qt_5_13_0 -)
%If (PyQt_SSL)
bool operator==(const QOcspResponse &lhs, const QOcspResponse &rhs);
%End
%End
%If (Qt_5_13_0 -)
%If (PyQt_SSL)
bool operator!=(const QOcspResponse &lhs, const QOcspResponse &rhs);
%End
%End
