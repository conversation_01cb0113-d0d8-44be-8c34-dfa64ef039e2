// qbarset.sip generated by MetaSIP
//
// This file is part of the QtChart Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtChart.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


namespace QtCharts
{
%TypeHeaderCode
#include <qbarset.h>
%End

    class QBarSet : public QObject
    {
%TypeHeaderCode
#include <qbarset.h>
%End

    public:
        QBarSet(const QString name, QObject *parent /TransferThis/ = 0);
        virtual ~QBarSet();
        void append(const qreal value);
        QtCharts::QBarSet &operator<<(const qreal &value);
        void insert(const int index, const qreal value);
        void replace(const int index, const qreal value);
        int count() const /__len__/;
        qreal sum() const;
        void setPen(const QPen &pen);
        QPen pen() const;
        void setBrush(const QBrush &brush);
        QBrush brush() const;
        void setLabelBrush(const QBrush &brush);
        QBrush labelBrush() const;
        void setLabelFont(const QFont &font);
        QFont labelFont() const;
        void setLabel(const QString label);
        QString label() const;
        void append(const QList<qreal> &values);
        void remove(const int index, const int count = 1);
        qreal at(const int index) const;
        qreal operator[](const int index) const;
%MethodCode
            SIP_SSIZE_T idx = sipConvertFromSequenceIndex(a0, sipCpp->count());
            
            if (idx < 0)
                sipIsErr = 1;
            else
                sipRes = sipCpp->operator[]((int)idx);
%End

        QColor color();
        void setColor(QColor color);
        QColor borderColor();
        void setBorderColor(QColor color);
        QColor labelColor();
        void setLabelColor(QColor color);

    signals:
        void penChanged();
        void brushChanged();
        void labelChanged();
        void labelBrushChanged();
        void labelFontChanged();
        void valuesAdded(int index, int count);
        void valuesRemoved(int index, int count);
        void valueChanged(int index);
        void clicked(int index);
%If (- QtChart_2_0_0)
        void hovered(bool status);
%End
%If (QtChart_1_3_1 -)
        void hovered(bool status, int index);
%End
        void colorChanged(QColor color);
        void borderColorChanged(QColor color);
        void labelColorChanged(QColor color);
%If (QtChart_2_0_0 -)
        void pressed(int index);
%End
%If (QtChart_2_0_0 -)
        void released(int index);
%End
%If (QtChart_2_0_0 -)
        void doubleClicked(int index);
%End
    };
};
