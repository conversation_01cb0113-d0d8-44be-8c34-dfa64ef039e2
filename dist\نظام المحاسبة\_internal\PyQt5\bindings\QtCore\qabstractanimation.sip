// qabstractanimation.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QAbstractAnimation : public QObject
{
%TypeHeaderCode
#include <qabstractanimation.h>
%End

public:
    enum Direction
    {
        Forward,
        Backward,
    };

    enum State
    {
        Stopped,
        Paused,
        Running,
    };

    enum DeletionPolicy
    {
        KeepWhenStopped,
        DeleteWhenStopped,
    };

    QAbstractAnimation(QObject *parent /TransferThis/ = 0);
    virtual ~QAbstractAnimation();
    QAbstractAnimation::State state() const;
    QAnimationGroup *group() const;
    QAbstractAnimation::Direction direction() const;
    void setDirection(QAbstractAnimation::Direction direction);
    int currentTime() const;
    int currentLoopTime() const;
    int loopCount() const;
    void setLoopCount(int loopCount);
    int currentLoop() const;
    virtual int duration() const = 0;
    int totalDuration() const;

signals:
    void finished();
    void stateChanged(QAbstractAnimation::State newState, QAbstractAnimation::State oldState);
    void currentLoopChanged(int currentLoop);
    void directionChanged(QAbstractAnimation::Direction);

public slots:
    void start(QAbstractAnimation::DeletionPolicy policy = QAbstractAnimation::KeepWhenStopped);
    void pause();
    void resume();
    void setPaused(bool);
    void stop();
    void setCurrentTime(int msecs);

protected:
    virtual bool event(QEvent *event);
    virtual void updateCurrentTime(int currentTime) = 0;
    virtual void updateState(QAbstractAnimation::State newState, QAbstractAnimation::State oldState);
    virtual void updateDirection(QAbstractAnimation::Direction direction);
};
