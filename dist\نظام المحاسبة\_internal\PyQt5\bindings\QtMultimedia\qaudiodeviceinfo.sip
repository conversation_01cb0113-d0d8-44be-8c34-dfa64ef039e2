// qaudiodeviceinfo.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QAudioDeviceInfo
{
%TypeHeaderCode
#include <qaudiodeviceinfo.h>
%End

public:
    QAudioDeviceInfo();
    QAudioDeviceInfo(const QAudioDeviceInfo &other);
    ~QAudioDeviceInfo();
    bool isNull() const;
    QString deviceName() const;
    bool isFormatSupported(const QAudioFormat &format) const;
    QAudioFormat preferredFormat() const;
    QAudioFormat nearestFormat(const QAudioFormat &format) const;
    QStringList supportedCodecs() const;
    QList<int> supportedSampleSizes() const;
    QList<QAudioFormat::Endian> supportedByteOrders() const;
    QList<QAudioFormat::SampleType> supportedSampleTypes() const;
    static QAudioDeviceInfo defaultInputDevice();
    static QAudioDeviceInfo defaultOutputDevice();
    static QList<QAudioDeviceInfo> availableDevices(QAudio::Mode mode);
    QList<int> supportedSampleRates() const;
    QList<int> supportedChannelCounts() const;
    bool operator==(const QAudioDeviceInfo &other) const;
    bool operator!=(const QAudioDeviceInfo &other) const;
%If (Qt_5_14_0 -)
    QString realm() const;
%End
};
