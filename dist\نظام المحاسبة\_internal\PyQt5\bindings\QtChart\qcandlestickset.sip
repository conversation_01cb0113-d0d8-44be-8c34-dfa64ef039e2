// qcandlestickset.sip generated by MetaSIP
//
// This file is part of the QtChart Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQtChart.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (QtChart_5_8_0 -)

namespace QtCharts
{
%TypeHeaderCode
#include <qcandlestickset.h>
%End

    class QCandlestickSet : public QObject
    {
%TypeHeaderCode
#include <qcandlestickset.h>
%End

    public:
        QCandlestickSet(qreal timestamp = 0., QObject *parent /TransferThis/ = 0);
        QCandlestickSet(qreal open, qreal high, qreal low, qreal close, qreal timestamp = 0., QObject *parent /TransferThis/ = 0);
        virtual ~QCandlestickSet();
        void setTimestamp(qreal timestamp);
        qreal timestamp() const;
        void setOpen(qreal open);
        qreal open() const;
        void setHigh(qreal high);
        qreal high() const;
        void setLow(qreal low);
        qreal low() const;
        void setClose(qreal close);
        qreal close() const;
        void setBrush(const QBrush &brush);
        QBrush brush() const;
        void setPen(const QPen &pen);
        QPen pen() const;

    signals:
        void clicked();
        void hovered(bool status);
        void pressed();
        void released();
        void doubleClicked();
        void timestampChanged();
        void openChanged();
        void highChanged();
        void lowChanged();
        void closeChanged();
        void brushChanged();
        void penChanged();
    };
};

%End
