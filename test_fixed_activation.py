#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النافذة المصححة
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_fixed_activation():
    """اختبار النافذة المصححة"""
    print("🔧 اختبار النافذة المصححة")
    print("=" * 50)
    
    # حذف ملفات التراخيص
    license_files = ["license.dat", "used_codes.dat"]
    for file in license_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"✅ تم حذف {file}")
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setLayoutDirection(2)  # RTL
        
        # استيراد النافذة المصححة
        from gui.activation_dialog import ActivationDialog
        
        # إنشاء النافذة
        dialog = ActivationDialog()
        
        print(f"✅ تم إنشاء النافذة المصححة")
        print(f"📏 حجم النافذة: {dialog.size().width()} x {dialog.size().height()}")
        
        # فحص حقل الإدخال
        if hasattr(dialog, 'activation_code'):
            print(f"✅ حقل كود التفعيل موجود")
            print(f"👁️ مرئي: {dialog.activation_code.isVisible()}")
            print(f"🔧 مفعل: {dialog.activation_code.isEnabled()}")
            print(f"📝 النص التوضيحي: {dialog.activation_code.placeholderText()}")
            print(f"📏 حجم الحقل: {dialog.activation_code.size().width()} x {dialog.activation_code.size().height()}")
        else:
            print(f"❌ حقل كود التفعيل غير موجود!")
            return False
        
        # فحص الأزرار
        if hasattr(dialog, 'activate_button'):
            print(f"✅ زر التفعيل موجود: {dialog.activate_button.text()}")
        
        # فحص معلومات الترخيص
        from license_manager import LicenseManager
        lm = LicenseManager()
        status = lm.check_license()
        
        print(f"\n📊 حالة الترخيص:")
        print(f"   - صالح: {status['valid']}")
        print(f"   - الحالة: {status['status']}")
        print(f"   - الرسالة: {status['message']}")
        
        if not status["valid"]:
            print(f"✅ البرنامج غير مفعل - يجب أن يظهر حقل الإدخال")
            
            # التحقق من حالة الحقل
            if dialog.activation_code.isVisible() and dialog.activation_code.isEnabled():
                print(f"🎉 حقل الإدخال مرئي ومفعل - الإصلاح نجح!")
                success = True
            else:
                print(f"❌ حقل الإدخال ما زال به مشكلة")
                print(f"   - مرئي: {dialog.activation_code.isVisible()}")
                print(f"   - مفعل: {dialog.activation_code.isEnabled()}")
                success = False
        else:
            print(f"⚠️ البرنامج مفعل - لن نرى حقل الإدخال")
            success = True
        
        # عرض النافذة
        print(f"\n🖥️ عرض النافذة للفحص البصري...")
        print(f"   📋 تحقق من:")
        print(f"   - ظهور رسالة 'البرنامج غير مفعل'")
        print(f"   - عرض بيانات الجهاز")
        print(f"   - ظهور حقل 'كود التفعيل' بوضوح")
        print(f"   - ظهور زر 'تفعيل البرنامج'")
        print(f"   - ظهور زر 'نسخ بيانات الجهاز'")
        print(f"\n   (اضغط Ctrl+C لإغلاق النافذة)")
        
        dialog.show()
        
        # تشغيل التطبيق
        result = app.exec_()
        
        if success:
            print(f"\n🎉 الإصلاح نجح! حقل الإدخال يظهر بشكل صحيح")
        else:
            print(f"\n⚠️ ما زالت هناك مشكلة في حقل الإدخال")
        
        return result == 0 and success
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النافذة: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار النافذة المصححة")
    print("=" * 60)
    
    success = test_fixed_activation()
    
    if success:
        print("\n✅ الاختبار نجح - النافذة تعمل بشكل صحيح!")
        print("🎉 يمكنك الآن تشغيل البرنامج الرئيسي واختبار التفعيل")
    else:
        print("\n❌ الاختبار فشل - ما زالت هناك مشكلة")
    
    return 0 if success else 1

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف الاختبار")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
