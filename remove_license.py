#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حذف الترخيص لاختبار أن البرنامج لا يعمل بدون ترخيص
"""

import sys
import os

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def remove_license():
    """حذف الترخيص الحالي"""
    print("🗑️ حذف الترخيص للاختبار")
    print("=" * 40)
    
    try:
        from license_manager import LicenseManager
        
        # إنشاء مدير التراخيص
        lm = LicenseManager()
        
        # فحص الترخيص الحالي
        status = lm.check_license()
        
        print(f"📊 حالة الترخيص قبل الحذف:")
        print(f"   - صالح: {status['valid']}")
        print(f"   - الحالة: {status['status']}")
        
        # حذف ملف الترخيص
        license_file = lm.license_file
        
        if os.path.exists(license_file):
            os.remove(license_file)
            print(f"✅ تم حذف ملف الترخيص: {license_file}")
        else:
            print(f"⚠️ ملف الترخيص غير موجود: {license_file}")
        
        # فحص الترخيص بعد الحذف
        status_after = lm.check_license()
        
        print(f"\n📊 حالة الترخيص بعد الحذف:")
        print(f"   - صالح: {status_after['valid']}")
        print(f"   - الحالة: {status_after['status']}")
        print(f"   - الرسالة: {status_after['message']}")
        
        if not status_after["valid"]:
            print("\n✅ تم حذف الترخيص بنجاح!")
            print("🚫 البرنامج الآن لن يعمل حتى يتم التفعيل")
            return True
        else:
            print("\n❌ خطأ: الترخيص ما زال صالح بعد الحذف!")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في حذف الترخيص: {e}")
        return False

def test_application_without_license():
    """اختبار التطبيق بدون ترخيص"""
    print("\n🧪 اختبار التطبيق بدون ترخيص...")
    
    try:
        from license_ui import check_license_and_show_dialog
        from license_manager import LicenseManager
        
        lm = LicenseManager()
        status = lm.check_license()
        
        if not status["valid"]:
            print("✅ check_license_and_show_dialog سيرجع False")
            print("✅ البرنامج سيعرض نافذة التفعيل ولن يعمل")
            print("✅ نظام الحماية يعمل بشكل صحيح")
            return True
        else:
            print("❌ check_license_and_show_dialog سيرجع True")
            print("❌ البرنامج سيعمل بدون ترخيص!")
            print("❌ نظام الحماية لا يعمل!")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التطبيق: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔒 حذف الترخيص لاختبار نظام الحماية")
    print("=" * 50)
    
    # حذف الترخيص
    if remove_license():
        print("\n" + "="*50)
        
        # اختبار التطبيق بدون ترخيص
        if test_application_without_license():
            print("\n🎉 نظام الحماية يعمل بشكل صحيح!")
            print("🔐 البرنامج محمي ولن يعمل بدون ترخيص صالح")
            print("\n📋 لاختبار البرنامج:")
            print("   1. شغل: python main.py")
            print("   2. ستظهر نافذة التفعيل")
            print("   3. إذا أغلقت النافذة، البرنامج لن يعمل")
            print("\n🔑 لإنشاء ترخيص صالح:")
            print("   python create_valid_license.py")
        else:
            print("\n❌ نظام الحماية لا يعمل بشكل صحيح!")
    else:
        print("\n❌ فشل في حذف الترخيص")

if __name__ == "__main__":
    main()
