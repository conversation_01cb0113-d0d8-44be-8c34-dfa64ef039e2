#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نافذة التفعيل المحدثة
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_activation_dialog():
    """اختبار نافذة التفعيل"""
    print("🧪 اختبار نافذة التفعيل المحدثة")
    print("=" * 50)
    
    try:
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        app.setLayoutDirection(2)  # RTL
        
        # استيراد نافذة التفعيل
        from gui.activation_dialog import ActivationDialog
        
        # إنشاء النافذة
        dialog = ActivationDialog()
        
        print("✅ تم إنشاء نافذة التفعيل بنجاح")
        print("📋 معلومات النافذة:")
        print(f"   - العنوان: {dialog.windowTitle()}")
        print(f"   - الحجم: {dialog.size().width()} x {dialog.size().height()}")
        
        # فحص الترخيص الحالي
        from license_manager import LicenseManager
        lm = LicenseManager()
        status = lm.check_license()
        
        print(f"\n📊 حالة الترخيص:")
        print(f"   - صالح: {status['valid']}")
        print(f"   - الحالة: {status['status']}")
        print(f"   - الرسالة: {status['message']}")
        
        if status["valid"]:
            print(f"   - ينتهي في: {status['expiry_date'].strftime('%d/%m/%Y')}")
            print(f"   - متبقي: {status['days_remaining']} يوم")
            print("\n✅ النافذة ستعرض معلومات الترخيص النشط")
        else:
            print("\n⚠️ النافذة ستعرض نموذج التفعيل")
        
        # عرض النافذة
        print(f"\n🖥️ عرض النافذة...")
        print("   (اضغط Ctrl+C لإغلاق النافذة)")
        
        dialog.show()
        
        # تشغيل التطبيق
        return app.exec_()
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في اختبار النافذة: {e}")
        return False

def test_license_info():
    """اختبار معلومات الترخيص"""
    print("\n🔍 اختبار معلومات الترخيص")
    print("=" * 30)
    
    try:
        from license_manager import LicenseManager
        
        lm = LicenseManager()
        customer_code = lm.get_customer_code()
        machine_id = lm.get_machine_id()
        
        print(f"🔑 كود العميل: {customer_code}")
        print(f"💻 رقم الجهاز: {machine_id}")
        
        # فحص الترخيص
        status = lm.check_license()
        
        if status["valid"]:
            print(f"✅ الترخيص صالح")
            print(f"📅 ينتهي في: {status['expiry_date'].strftime('%d/%m/%Y')}")
            print(f"⏰ متبقي: {status['days_remaining']} يوم")
            
            # تحديد نوع العرض في النافذة
            if status['days_remaining'] <= 7:
                print("⚠️ النافذة ستعرض تحذير انتهاء الصلاحية")
            elif status['days_remaining'] <= 30:
                print("⚠️ النافذة ستعرض تنبيه اقتراب انتهاء الصلاحية")
            else:
                print("✅ النافذة ستعرض حالة طبيعية")
        else:
            print(f"❌ الترخيص غير صالح: {status['message']}")
            print("🔑 النافذة ستعرض نموذج التفعيل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص معلومات الترخيص: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔐 اختبار نافذة التفعيل المحدثة")
    print("=" * 60)
    
    # اختبار معلومات الترخيص أولاً
    if not test_license_info():
        print("❌ فشل في اختبار معلومات الترخيص")
        return 1
    
    # اختبار النافذة
    print(f"\n" + "="*60)
    result = test_activation_dialog()
    
    if result == 0:
        print("\n✅ تم إغلاق النافذة بنجاح")
    else:
        print(f"\n⚠️ النافذة أُغلقت بالكود: {result}")
    
    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n⚠️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
