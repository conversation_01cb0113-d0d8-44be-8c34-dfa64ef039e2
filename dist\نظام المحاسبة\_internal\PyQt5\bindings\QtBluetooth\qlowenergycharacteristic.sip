// qlowenergycharacteristic.sip generated by MetaSIP
//
// This file is part of the QtBluetooth Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_4_0 -)

class QLowEnergyCharacteristic
{
%TypeHeaderCode
#include <qlowenergycharacteristic.h>
%End

public:
    enum PropertyType
    {
        Unknown,
        Broadcasting,
        Read,
        WriteNoResponse,
        Write,
        Notify,
        Indicate,
        WriteSigned,
        ExtendedProperty,
    };

    typedef QFlags<QLowEnergyCharacteristic::PropertyType> PropertyTypes;
    QLowEnergyCharacteristic();
    QLowEnergyCharacteristic(const QLowEnergyCharacteristic &other);
    ~QLowEnergyCharacteristic();
    bool operator==(const QLowEnergyCharacteristic &other) const;
    bool operator!=(const QLowEnergyCharacteristic &other) const;
    QString name() const;
    QBluetoothUuid uuid() const;
    QByteArray value() const;
    QLowEnergyCharacteristic::PropertyTypes properties() const;
    QLowEnergyHandle handle() const;
    QLowEnergyDescriptor descriptor(const QBluetoothUuid &uuid) const;
    QList<QLowEnergyDescriptor> descriptors() const;
    bool isValid() const;
};

%End
%If (Qt_5_4_0 -)
QFlags<QLowEnergyCharacteristic::PropertyType> operator|(QLowEnergyCharacteristic::PropertyType f1, QFlags<QLowEnergyCharacteristic::PropertyType> f2);
%End
