// qdir.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file L<PERSON><PERSON><PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDir
{
%TypeHeaderCode
#include <qdir.h>
%End

public:
    enum Filter
    {
        Dirs,
        Files,
        Drives,
        NoSymLinks,
        AllEntries,
        TypeMask,
        Readable,
        Writable,
        Executable,
        PermissionMask,
        Modified,
        Hidden,
        System,
        AccessMask,
        AllDirs,
        CaseSensitive,
        NoDotAndDotDot,
        NoFilter,
        NoDot,
        NoDotDot,
    };

    typedef QFlags<QDir::Filter> Filters;

    enum SortFlag
    {
        Name,
        Time,
        Size,
        Unsorted,
        SortByMask,
        DirsFirst,
        Reversed,
        IgnoreCase,
        DirsLast,
        LocaleAware,
        Type,
        NoSort,
    };

    typedef QFlags<QDir::SortFlag> SortFlags;
    QDir(const QDir &);
    QDir(const QString &path = QString());
    QDir(const QString &path, const QString &nameFilter, QFlags<QDir::SortFlag> sort /TypeHintValue="QDir.Name|QDir.IgnoreCase"/ = QDir::SortFlags(QDir::Name|QDir::IgnoreCase), QFlags<QDir::Filter> filters = AllEntries);
    ~QDir();
    void setPath(const QString &path);
    QString path() const;
    QString absolutePath() const;
    QString canonicalPath() const;
    QString dirName() const;
    QString filePath(const QString &fileName) const;
    QString absoluteFilePath(const QString &fileName) const;
    QString relativeFilePath(const QString &fileName) const;
    bool cd(const QString &dirName);
    bool cdUp();
    QStringList nameFilters() const;
    void setNameFilters(const QStringList &nameFilters);
    QDir::Filters filter() const;
    void setFilter(QDir::Filters filter);
    QDir::SortFlags sorting() const;
    void setSorting(QDir::SortFlags sort);
    uint count() const /__len__/;
    QString operator[](int) const;
%MethodCode
        Py_ssize_t idx = sipConvertFromSequenceIndex(a0, sipCpp->count());
        
        if (idx < 0)
            sipIsErr = 1;
        else
            sipRes = new QString(sipCpp->operator[]((int)idx));
%End

    QStringList operator[](SIP_PYSLICE) const;
%MethodCode
        Py_ssize_t start, stop, step, slicelength;
        
        if (sipConvertFromSliceObject(a0, sipCpp->count(), &start, &stop, &step, &slicelength) < 0)
        {
            sipIsErr = 1;
        }
        else
        {
            sipRes = new QStringList();
        
            for (Py_ssize_t i = 0; i < slicelength; ++i)
            {
                (*sipRes) += (*sipCpp)[start];
                start += step;
            }
        }
%End

    int __contains__(const QString &) const;
%MethodCode
        sipRes = bool(sipCpp->entryList().contains(*a0));
%End

    static QStringList nameFiltersFromString(const QString &nameFilter);
    QStringList entryList(QDir::Filters filters = QDir::NoFilter, QDir::SortFlags sort = QDir::SortFlag::NoSort) const;
    QStringList entryList(const QStringList &nameFilters, QDir::Filters filters = QDir::NoFilter, QDir::SortFlags sort = QDir::SortFlag::NoSort) const;
    QFileInfoList entryInfoList(QDir::Filters filters = QDir::NoFilter, QDir::SortFlags sort = QDir::SortFlag::NoSort) const;
    QFileInfoList entryInfoList(const QStringList &nameFilters, QDir::Filters filters = QDir::NoFilter, QDir::SortFlags sort = QDir::SortFlag::NoSort) const;
    bool mkdir(const QString &dirName) const;
    bool rmdir(const QString &dirName) const;
    bool mkpath(const QString &dirPath) const;
    bool rmpath(const QString &dirPath) const;
    bool isReadable() const;
    bool exists() const;
    bool isRoot() const;
    static bool isRelativePath(const QString &path);
    static bool isAbsolutePath(const QString &path);
    bool isRelative() const;
    bool isAbsolute() const;
    bool makeAbsolute();
    bool operator==(const QDir &dir) const;
    bool operator!=(const QDir &dir) const;
    bool remove(const QString &fileName);
    bool rename(const QString &oldName, const QString &newName);
    bool exists(const QString &name) const;
    void refresh() const;
    static QFileInfoList drives();
    static QChar separator();
    static bool setCurrent(const QString &path);
    static QDir current();
    static QString currentPath();
    static QDir home();
    static QString homePath();
    static QDir root();
    static QString rootPath();
    static QDir temp();
    static QString tempPath();
    static bool match(const QStringList &filters, const QString &fileName);
    static bool match(const QString &filter, const QString &fileName);
    static QString cleanPath(const QString &path);
    static QString toNativeSeparators(const QString &pathName);
    static QString fromNativeSeparators(const QString &pathName);
    static void setSearchPaths(const QString &prefix, const QStringList &searchPaths);
    static void addSearchPath(const QString &prefix, const QString &path);
    static QStringList searchPaths(const QString &prefix);
    bool removeRecursively();
    void swap(QDir &other /Constrained/);
%If (Qt_5_6_0 -)
    static QChar listSeparator();
%End
%If (Qt_5_9_0 -)
    bool isEmpty(QDir::Filters filters = QDir::AllEntries | QDir::NoDotAndDotDot) const;
%End
};

QFlags<QDir::Filter> operator|(QDir::Filter f1, QFlags<QDir::Filter> f2);
QFlags<QDir::SortFlag> operator|(QDir::SortFlag f1, QFlags<QDir::SortFlag> f2);
