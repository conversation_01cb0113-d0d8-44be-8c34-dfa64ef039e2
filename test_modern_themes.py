#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الثيمات الحديثة الجديدة
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QFrame, QHBoxLayout
from PyQt5.QtCore import Qt

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.modern_theme_manager import modern_theme_manager

class ThemeTestWindow(QMainWindow):
    """نافذة اختبار الثيمات"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار الثيمات الحديثة")
        self.setGeometry(100, 100, 800, 600)
        
        # إنشاء الواجهة
        self.setup_ui()
        
        # تطبيق الثيم الافتراضي
        self.apply_current_theme()
    
    def setup_ui(self):
        """إنشاء واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # عنوان
        title = QLabel("🎨 اختبار الثيمات الحديثة")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 24px; font-weight: bold; margin: 20px;")
        layout.addWidget(title)
        
        # أزرار الثيمات
        themes_frame = QFrame()
        themes_layout = QHBoxLayout()
        themes_frame.setLayout(themes_layout)
        
        available_themes = modern_theme_manager.get_available_themes()
        
        for theme_id, theme_info in available_themes.items():
            button = QPushButton(theme_info['name'])
            button.clicked.connect(lambda checked, tid=theme_id: self.change_theme(tid))
            button.setMinimumHeight(50)
            themes_layout.addWidget(button)
        
        layout.addWidget(themes_frame)
        
        # منطقة المحتوى للاختبار
        content_frame = QFrame()
        content_frame.setObjectName("content_frame")
        content_layout = QVBoxLayout()
        content_frame.setLayout(content_layout)
        
        # كروت اختبار
        for i in range(3):
            card = QFrame()
            card.setObjectName(f"test_card_{i}")
            card_layout = QVBoxLayout()
            card.setLayout(card_layout)
            
            card_title = QLabel(f"كارت اختبار {i + 1}")
            card_title.setStyleSheet("font-size: 18px; font-weight: bold;")
            card_layout.addWidget(card_title)
            
            card_content = QLabel("هذا محتوى تجريبي لاختبار الثيم الحديث")
            card_layout.addWidget(card_content)
            
            card_button = QPushButton("زر تجريبي")
            card_layout.addWidget(card_button)
            
            content_layout.addWidget(card)
        
        layout.addWidget(content_frame)
        
        # معلومات الثيم الحالي
        self.theme_info_label = QLabel()
        self.theme_info_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.theme_info_label)
    
    def change_theme(self, theme_id: str):
        """تغيير الثيم"""
        if modern_theme_manager.set_theme(theme_id):
            self.apply_current_theme()
            print(f"تم تغيير الثيم إلى: {theme_id}")
    
    def apply_current_theme(self):
        """تطبيق الثيم الحالي"""
        # تطبيق الثيم العام
        self.setStyleSheet(modern_theme_manager.get_stylesheet("general"))
        
        # تطبيق ثيم الأزرار
        for button in self.findChildren(QPushButton):
            button.setStyleSheet(modern_theme_manager.get_stylesheet("button"))
        
        # تطبيق ثيم الكروت
        for card in self.findChildren(QFrame):
            if card.objectName().startswith("test_card"):
                card.setStyleSheet(modern_theme_manager.get_stylesheet("card"))
        
        # تحديث معلومات الثيم
        theme_info = modern_theme_manager.get_current_theme_info()
        self.theme_info_label.setText(f"الثيم الحالي: {theme_info['name']} - {theme_info['description']}")
        
        # إعادة رسم النافذة
        self.update()

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء النافذة
    window = ThemeTestWindow()
    window.show()
    
    print("🎨 نافذة اختبار الثيمات الحديثة")
    print("=" * 50)
    
    # عرض الثيمات المتاحة
    themes = modern_theme_manager.get_available_themes()
    print("الثيمات المتاحة:")
    for theme_id, theme_info in themes.items():
        current = " (الحالي)" if theme_id == modern_theme_manager.current_theme else ""
        print(f"  • {theme_info['name']}{current}")
        print(f"    {theme_info['description']}")
    
    print(f"\nالثيم الحالي: {modern_theme_manager.current_theme}")
    print("\nاستخدم الأزرار في النافذة لتجربة الثيمات المختلفة!")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
