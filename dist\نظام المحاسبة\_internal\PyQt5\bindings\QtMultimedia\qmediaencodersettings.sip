// qmediaencodersettings.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QAudioEncoderSettings
{
%TypeHeaderCode
#include <qmediaencodersettings.h>
%End

public:
    QAudioEncoderSettings();
    QAudioEncoderSettings(const QAudioEncoderSettings &other);
    ~QAudioEncoderSettings();
    bool operator==(const QAudioEncoderSettings &other) const;
    bool operator!=(const QAudioEncoderSettings &other) const;
    bool isNull() const;
    QMultimedia::EncodingMode encodingMode() const;
    void setEncodingMode(QMultimedia::EncodingMode);
    QString codec() const;
    void setCodec(const QString &codec);
    int bitRate() const;
    void setBitRate(int bitrate);
    int channelCount() const;
    void setChannelCount(int channels);
    int sampleRate() const;
    void setSampleRate(int rate);
    QMultimedia::EncodingQuality quality() const;
    void setQuality(QMultimedia::EncodingQuality quality);
    QVariant encodingOption(const QString &option) const;
    QVariantMap encodingOptions() const;
    void setEncodingOption(const QString &option, const QVariant &value);
    void setEncodingOptions(const QVariantMap &options);
};

class QVideoEncoderSettings
{
%TypeHeaderCode
#include <qmediaencodersettings.h>
%End

public:
    QVideoEncoderSettings();
    QVideoEncoderSettings(const QVideoEncoderSettings &other);
    ~QVideoEncoderSettings();
    bool operator==(const QVideoEncoderSettings &other) const;
    bool operator!=(const QVideoEncoderSettings &other) const;
    bool isNull() const;
    QMultimedia::EncodingMode encodingMode() const;
    void setEncodingMode(QMultimedia::EncodingMode);
    QString codec() const;
    void setCodec(const QString &);
    QSize resolution() const;
    void setResolution(const QSize &);
    void setResolution(int width, int height);
    qreal frameRate() const;
    void setFrameRate(qreal rate);
    int bitRate() const;
    void setBitRate(int bitrate);
    QMultimedia::EncodingQuality quality() const;
    void setQuality(QMultimedia::EncodingQuality quality);
    QVariant encodingOption(const QString &option) const;
    QVariantMap encodingOptions() const;
    void setEncodingOption(const QString &option, const QVariant &value);
    void setEncodingOptions(const QVariantMap &options);
};

class QImageEncoderSettings
{
%TypeHeaderCode
#include <qmediaencodersettings.h>
%End

public:
    QImageEncoderSettings();
    QImageEncoderSettings(const QImageEncoderSettings &other);
    ~QImageEncoderSettings();
    bool operator==(const QImageEncoderSettings &other) const;
    bool operator!=(const QImageEncoderSettings &other) const;
    bool isNull() const;
    QString codec() const;
    void setCodec(const QString &);
    QSize resolution() const;
    void setResolution(const QSize &);
    void setResolution(int width, int height);
    QMultimedia::EncodingQuality quality() const;
    void setQuality(QMultimedia::EncodingQuality quality);
    QVariant encodingOption(const QString &option) const;
    QVariantMap encodingOptions() const;
    void setEncodingOption(const QString &option, const QVariant &value);
    void setEncodingOptions(const QVariantMap &options);
};
