// qaudiodecoder.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QAudioDecoder : public QMediaObject
{
%TypeHeaderCode
#include <qaudiodecoder.h>
%End

public:
    enum State
    {
        StoppedState,
        DecodingState,
    };

    enum Error
    {
        NoError,
        ResourceError,
        FormatError,
        AccessDeniedError,
        ServiceMissingError,
    };

%If (Qt_5_6_1 -)
    explicit QAudioDecoder(QObject *parent /TransferThis/ = 0);
%End
%If (- Qt_5_6_1)
    QAudioDecoder(QObject *parent /TransferThis/ = 0);
%End
    virtual ~QAudioDecoder();
    static QMultimedia::SupportEstimate hasSupport(const QString &mimeType, const QStringList &codecs = QStringList());
    QAudioDecoder::State state() const;
    QString sourceFilename() const;
    void setSourceFilename(const QString &fileName);
    QIODevice *sourceDevice() const;
    void setSourceDevice(QIODevice *device);
    QAudioFormat audioFormat() const;
    void setAudioFormat(const QAudioFormat &format);
    QAudioDecoder::Error error() const;
    QString errorString() const;
    QAudioBuffer read() const;
    bool bufferAvailable() const;
    qint64 position() const;
    qint64 duration() const;

public slots:
    void start();
    void stop();

signals:
    void bufferAvailableChanged(bool);
    void bufferReady();
    void finished();
    void stateChanged(QAudioDecoder::State newState);
    void formatChanged(const QAudioFormat &format);
    void error(QAudioDecoder::Error error);
    void sourceChanged();
    void positionChanged(qint64 position);
    void durationChanged(qint64 duration);

public:
    virtual bool bind(QObject *);
    virtual void unbind(QObject *);
};
