// qmediastreamscontrol.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QMediaStreamsControl : public QMediaControl
{
%TypeHeaderCode
#include <qmediastreamscontrol.h>
%End

public:
    enum StreamType
    {
        UnknownStream,
        VideoStream,
        AudioStream,
        SubPictureStream,
        DataStream,
    };

    virtual ~QMediaStreamsControl();
    virtual int streamCount() = 0;
    virtual QMediaStreamsControl::StreamType streamType(int streamNumber) = 0;
    virtual QVariant metaData(int streamNumber, const QString &key) = 0;
    virtual bool isActive(int streamNumber) = 0;
    virtual void setActive(int streamNumber, bool state) = 0;

signals:
    void streamsChanged();
    void activeStreamsChanged();

protected:
    explicit QMediaStreamsControl(QObject *parent /TransferThis/ = 0);
};
