// qmediarecorder.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QMediaRecorder : public QObject, public QMediaBindableInterface
{
%TypeHeaderCode
#include <qmediarecorder.h>
%End

public:
    enum State
    {
        StoppedState,
        RecordingState,
        PausedState,
    };

    enum Status
    {
        UnavailableStatus,
        UnloadedStatus,
        LoadingStatus,
        LoadedStatus,
        StartingStatus,
        RecordingStatus,
        PausedStatus,
        FinalizingStatus,
    };

    enum Error
    {
        NoError,
        ResourceError,
        FormatError,
        OutOfSpaceError,
    };

    QMediaRecorder(QMediaObject *mediaObject, QObject *parent /TransferThis/ = 0);
    virtual ~QMediaRecorder();
    virtual QMediaObject *mediaObject() const;
    bool isAvailable() const;
    QMultimedia::AvailabilityStatus availability() const;
    QUrl outputLocation() const;
    bool setOutputLocation(const QUrl &location);
    QUrl actualLocation() const;
    QMediaRecorder::State state() const;
    QMediaRecorder::Status status() const;
    QMediaRecorder::Error error() const;
    QString errorString() const;
    qint64 duration() const;
    bool isMuted() const;
    qreal volume() const;
    QStringList supportedContainers() const;
    QString containerDescription(const QString &format) const;
    QStringList supportedAudioCodecs() const;
    QString audioCodecDescription(const QString &codecName) const;
    QList<int> supportedAudioSampleRates(const QAudioEncoderSettings &settings = QAudioEncoderSettings(), bool *continuous = 0) const;
    QStringList supportedVideoCodecs() const;
    QString videoCodecDescription(const QString &codecName) const;
    QList<QSize> supportedResolutions(const QVideoEncoderSettings &settings = QVideoEncoderSettings(), bool *continuous = 0) const;
    QList<qreal> supportedFrameRates(const QVideoEncoderSettings &settings = QVideoEncoderSettings(), bool *continuous = 0) const;
    QAudioEncoderSettings audioSettings() const;
    QVideoEncoderSettings videoSettings() const;
    QString containerFormat() const;
    void setAudioSettings(const QAudioEncoderSettings &audioSettings);
    void setVideoSettings(const QVideoEncoderSettings &videoSettings);
    void setContainerFormat(const QString &container);
    void setEncodingSettings(const QAudioEncoderSettings &audio, const QVideoEncoderSettings &video = QVideoEncoderSettings(), const QString &container = QString());
    bool isMetaDataAvailable() const;
    bool isMetaDataWritable() const;
    QVariant metaData(const QString &key) const;
    void setMetaData(const QString &key, const QVariant &value);
    QStringList availableMetaData() const;

public slots:
    void record();
    void pause();
    void stop();
    void setMuted(bool muted);
    void setVolume(qreal volume);

signals:
    void stateChanged(QMediaRecorder::State state);
    void statusChanged(QMediaRecorder::Status status);
    void durationChanged(qint64 duration);
    void mutedChanged(bool muted);
    void volumeChanged(qreal volume);
    void actualLocationChanged(const QUrl &location);
    void error(QMediaRecorder::Error error);
    void metaDataAvailableChanged(bool available);
    void metaDataWritableChanged(bool writable);
    void metaDataChanged(const QString &key, const QVariant &value);
    void metaDataChanged();
    void availabilityChanged(QMultimedia::AvailabilityStatus availability /Constrained/);
    void availabilityChanged(bool available);

protected:
    virtual bool setMediaObject(QMediaObject *object);
};
