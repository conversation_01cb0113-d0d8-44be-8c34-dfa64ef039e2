# 🔐 دليل نظام التراخيص الاحترافي

## 📋 نظرة عامة

تم تطوير نظام ترخيص احترافي يتكون من جزأين:
1. **برنامج إصدار التراخيص** (للمطور)
2. **نظام التفعيل في البرنامج الأصلي** (للعميل)

---

## 🔧 للمطور: برنامج إصدار التراخيص

### 🚀 تشغيل البرنامج:
```bash
python license_generator_app.py
```

### 📝 خطوات إصدار ترخيص جديد:

1. **استلام بيانات العميل:**
   - كود العميل (من البرنامج)
   - رقم الجهاز (من البرنامج)
   - اسم العميل

2. **إدخال البيانات في البرنامج:**
   - 👤 اسم العميل
   - 🔑 كود العميل (نسخ من إيميل العميل)
   - 💻 رقم الجهاز (نسخ من إيميل العميل)
   - ⏰ مدة الترخيص بالأيام (افتراضي: 365)

3. **إصدار الكود:**
   - اضغط "🔑 إصدار كود الترخيص"
   - سيظهر كود الترخيص في المنطقة السفلى
   - انسخ الكود وأرسله للعميل

### 📊 مميزات برنامج إصدار التراخيص:

- ✅ **قاعدة بيانات التراخيص**: حفظ جميع التراخيص المصدرة
- ✅ **تتبع الحالة**: عرض التراخيص النشطة والمنتهية
- ✅ **أمان عالي**: تشفير متقدم للأكواد
- ✅ **سهولة الاستخدام**: واجهة بسيطة وواضحة

---

## 👤 للعميل: عملية التفعيل

### 🔒 عند فتح البرنامج بدون ترخيص:

1. **تظهر نافذة "البرنامج غير مفعل":**
   - 🔑 كود العميل
   - 💻 رقم الجهاز
   - 📞 تعليمات التواصل

2. **خطوات الحصول على الترخيص:**
   - اضغط "📋 نسخ بيانات الجهاز"
   - أرسل البيانات إلى: <EMAIL>
   - انتظر كود التفعيل (خلال 24 ساعة)

3. **تفعيل البرنامج:**
   - اضغط "🔑 تفعيل البرنامج"
   - أدخل كود التفعيل المستلم
   - اضغط "✅ تفعيل"

### ✅ نتائج التفعيل:

**إذا كان الكود صحيح:**
- ✅ رسالة نجاح التفعيل
- 📅 عرض تاريخ انتهاء الترخيص
- 🚀 فتح البرنامج تلقائياً

**إذا كان الكود خاطئ:**
- ❌ رسالة خطأ
- 🚫 البرنامج لا يفتح
- 🔄 إمكانية المحاولة مرة أخرى

---

## 🔐 تنسيق كود الترخيص

```
SICOO-YYYYMMDD-CUSTOMER-MACHINE-HASH
```

**مثال:**
```
SICOO-20250715-B3823B-CAA0ED-A1B2C3D4
```

**شرح الأجزاء:**
- `SICOO`: اسم الشركة
- `20250715`: تاريخ انتهاء الترخيص (15/07/2025)
- `B3823B`: أول 6 أحرف من كود العميل
- `CAA0ED`: أول 6 أحرف من رقم الجهاز
- `A1B2C3D4`: hash للتحقق من صحة الكود

---

## 🛡️ مميزات الأمان

### 🔒 حماية متقدمة:
- ✅ **ربط بالجهاز**: الترخيص يعمل على جهاز واحد فقط
- ✅ **تشفير قوي**: استخدام SHA-256 للتحقق
- ✅ **تاريخ انتهاء**: منع الاستخدام بعد انتهاء الترخيص
- ✅ **منع النسخ**: لا يمكن نسخ الترخيص لجهاز آخر

### 🚫 حماية من التلاعب:
- ❌ **لا يمكن تعديل التاريخ**: النظام يتحقق من التاريخ الفعلي
- ❌ **لا يمكن نسخ الملفات**: الترخيص مرتبط بالجهاز
- ❌ **لا يمكن تجاوز النظام**: البرنامج لا يعمل بدون ترخيص صالح

---

## 📁 الملفات المهمة

### للمطور:
- `license_generator_app.py` - برنامج إصدار التراخيص
- `issued_licenses.json` - قاعدة بيانات التراخيص المصدرة

### للعميل:
- `license.dat` - ملف الترخيص (يُنشأ تلقائياً)
- `license_ui.py` - واجهة التفعيل
- `license_manager.py` - مدير التراخيص

---

## 🧪 أدوات الاختبار

### للمطور:
```bash
# اختبار نظام الترخيص
python test_license_enforcement.py

# حذف الترخيص للاختبار
python remove_license.py

# إنشاء ترخيص للاختبار
python create_valid_license.py
```

---

## 📞 الدعم الفني

### للعملاء:
- 📧 **البريد الإلكتروني**: <EMAIL>
- 📋 **المطلوب في الإيميل**:
  - كود العميل ورقم الجهاز
  - اسم العميل أو الشركة
  - رقم الهاتف للتواصل

### للمطور:
- 🔧 **صيانة النظام**: فحص دوري لقاعدة بيانات التراخيص
- 📊 **تقارير الاستخدام**: مراقبة التراخيص النشطة والمنتهية
- 🔄 **تحديث النظام**: إضافة مميزات جديدة حسب الحاجة

---

## 🎯 الخلاصة

✅ **نظام ترخيص احترافي وآمن**
✅ **سهولة في الاستخدام للعميل**
✅ **أدوات متقدمة للمطور**
✅ **حماية قوية ضد التلاعب**
✅ **دعم فني شامل**

🎉 **النظام جاهز للاستخدام الفعلي!**
