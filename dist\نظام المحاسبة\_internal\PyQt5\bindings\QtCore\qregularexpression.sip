// qregularexpression.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QRegularExpression
{
%TypeHeaderCode
#include <qregularexpression.h>
%End

public:
    enum PatternOption
    {
        NoPatternOption,
        CaseInsensitiveOption,
        DotMatchesEverythingOption,
        MultilineOption,
        ExtendedPatternSyntaxOption,
        InvertedGreedinessOption,
        DontCaptureOption,
        UseUnicodePropertiesOption,
%If (Qt_5_4_0 -)
        OptimizeOnFirstUsageOption,
%End
%If (Qt_5_4_0 -)
        DontAutomaticallyOptimizeOption,
%End
    };

    typedef QFlags<QRegularExpression::PatternOption> PatternOptions;
    QRegularExpression::PatternOptions patternOptions() const;
    void setPatternOptions(QRegularExpression::PatternOptions options);
    QRegularExpression();
    QRegularExpression(const QString &pattern, QRegularExpression::PatternOptions options = QRegularExpression::NoPatternOption);
    QRegularExpression(const QRegularExpression &re);
    ~QRegularExpression();
    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        PyObject *uni = qpycore_PyObject_FromQString(sipCpp->pattern());
        
        if (uni)
        {
        #if PY_MAJOR_VERSION >= 3
            sipRes = PyUnicode_FromFormat("PyQt5.QtCore.QRegularExpression(%R", uni);
        
            if (sipCpp->patternOptions() != QRegularExpression::NoPatternOption)
            {
                qpycore_Unicode_ConcatAndDel(&sipRes,
                        PyUnicode_FromFormat(
                                ", PyQt5.QtCore.QRegularExpression.PatternOptions(%i)",
                                (int)sipCpp->patternOptions()));
            }
        
            qpycore_Unicode_ConcatAndDel(&sipRes, PyUnicode_FromString(")"));
        #else
            sipRes = PyString_FromString("PyQt5.QtCore.QRegularExpression(");
            PyString_ConcatAndDel(&sipRes, PyObject_Repr(uni));
        
            if (sipCpp->patternOptions() != QRegularExpression::NoPatternOption)
            {
                PyString_ConcatAndDel(&sipRes,
                        PyString_FromFormat(
                                ", PyQt5.QtCore.QRegularExpression.PatternOptions(%i)",
                                (int)sipCpp->patternOptions()));
            }
        
            PyString_ConcatAndDel(&sipRes, PyString_FromString(")"));
        #endif
        
            Py_DECREF(uni);
        }
        else
        {
            sipRes = 0;
        }
%End

    void swap(QRegularExpression &re /Constrained/);
    QString pattern() const;
    void setPattern(const QString &pattern);
    bool isValid() const;
    int patternErrorOffset() const;
    QString errorString() const;
    int captureCount() const;

    enum MatchType
    {
        NormalMatch,
        PartialPreferCompleteMatch,
        PartialPreferFirstMatch,
%If (Qt_5_1_0 -)
        NoMatch,
%End
    };

    enum MatchOption
    {
        NoMatchOption,
        AnchoredMatchOption,
%If (Qt_5_4_0 -)
        DontCheckSubjectStringMatchOption,
%End
    };

    typedef QFlags<QRegularExpression::MatchOption> MatchOptions;
    QRegularExpressionMatch match(const QString &subject, int offset = 0, QRegularExpression::MatchType matchType = QRegularExpression::NormalMatch, QRegularExpression::MatchOptions matchOptions = QRegularExpression::NoMatchOption) const;
    QRegularExpressionMatchIterator globalMatch(const QString &subject, int offset = 0, QRegularExpression::MatchType matchType = QRegularExpression::NormalMatch, QRegularExpression::MatchOptions matchOptions = QRegularExpression::NoMatchOption) const;
    static QString escape(const QString &str);
%If (Qt_5_1_0 -)
    QStringList namedCaptureGroups() const;
%End
    bool operator==(const QRegularExpression &re) const;
    bool operator!=(const QRegularExpression &re) const;
%If (Qt_5_4_0 -)
    void optimize() const;
%End
%If (Qt_5_6_0 -)
    long __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End

%End
%If (Qt_5_12_0 -)
    static QString wildcardToRegularExpression(const QString &str);
%End
%If (Qt_5_12_0 -)
    static QString anchoredPattern(const QString &expression);
%End
};

QFlags<QRegularExpression::PatternOption> operator|(QRegularExpression::PatternOption f1, QFlags<QRegularExpression::PatternOption> f2);
QFlags<QRegularExpression::MatchOption> operator|(QRegularExpression::MatchOption f1, QFlags<QRegularExpression::MatchOption> f2);
QDataStream &operator<<(QDataStream &out, const QRegularExpression &re /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &in, QRegularExpression &re /Constrained/) /ReleaseGIL/;

class QRegularExpressionMatch
{
%TypeHeaderCode
#include <qregularexpression.h>
%End

public:
%If (Qt_5_1_0 -)
    QRegularExpressionMatch();
%End
    ~QRegularExpressionMatch();
    QRegularExpressionMatch(const QRegularExpressionMatch &match);
    void swap(QRegularExpressionMatch &match /Constrained/);
    QRegularExpression regularExpression() const;
    QRegularExpression::MatchType matchType() const;
    QRegularExpression::MatchOptions matchOptions() const;
    bool hasMatch() const;
    bool hasPartialMatch() const;
    bool isValid() const;
    int lastCapturedIndex() const;
    QString captured(int nth = 0) const;
    QString captured(const QString &name) const;
    QStringList capturedTexts() const;
    int capturedStart(int nth = 0) const;
    int capturedLength(int nth = 0) const;
    int capturedEnd(int nth = 0) const;
    int capturedStart(const QString &name) const;
    int capturedLength(const QString &name) const;
    int capturedEnd(const QString &name) const;
};

class QRegularExpressionMatchIterator
{
%TypeHeaderCode
#include <qregularexpression.h>
%End

public:
%If (Qt_5_1_0 -)
    QRegularExpressionMatchIterator();
%End
    ~QRegularExpressionMatchIterator();
    QRegularExpressionMatchIterator(const QRegularExpressionMatchIterator &iterator);
    void swap(QRegularExpressionMatchIterator &iterator /Constrained/);
    bool isValid() const;
    bool hasNext() const;
    QRegularExpressionMatch next();
    QRegularExpressionMatch peekNext() const;
    QRegularExpression regularExpression() const;
    QRegularExpression::MatchType matchType() const;
    QRegularExpression::MatchOptions matchOptions() const;
};
