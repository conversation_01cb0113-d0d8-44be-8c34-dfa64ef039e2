// qabstracttextdocumentlayout.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QAbstractTextDocumentLayout : public QObject
{
%TypeHeaderCode
#include <qabstracttextdocumentlayout.h>
%End

public:
    explicit QAbstractTextDocumentLayout(QTextDocument *doc);
    virtual ~QAbstractTextDocumentLayout();

    struct Selection
    {
%TypeHeaderCode
#include <qabstracttextdocumentlayout.h>
%End

        QTextCursor cursor;
        QTextCharFormat format;
    };

    struct PaintContext
    {
%TypeHeaderCode
#include <qabstracttextdocumentlayout.h>
%End

        PaintContext();
        int cursorPosition;
        QPalette palette;
        QRectF clip;
        QVector<QAbstractTextDocumentLayout::Selection> selections;
    };

    virtual void draw(QPainter *painter, const QAbstractTextDocumentLayout::PaintContext &context) = 0;
    virtual int hitTest(const QPointF &point, Qt::HitTestAccuracy accuracy) const = 0;
    QString anchorAt(const QPointF &pos) const;
    virtual int pageCount() const = 0;
    virtual QSizeF documentSize() const = 0;
    virtual QRectF frameBoundingRect(QTextFrame *frame) const = 0;
    virtual QRectF blockBoundingRect(const QTextBlock &block) const = 0;
    void setPaintDevice(QPaintDevice *device);
    QPaintDevice *paintDevice() const;
    QTextDocument *document() const;
    void registerHandler(int objectType, QObject *component);
%If (Qt_5_2_0 -)
    void unregisterHandler(int objectType, QObject *component = 0);
%End
    QTextObjectInterface *handlerForObject(int objectType) const;

signals:
    void update(const QRectF &rect = QRectF(0., 0., 1.0E+9, 1.0E+9));
    void documentSizeChanged(const QSizeF &newSize);
    void pageCountChanged(int newPages);
    void updateBlock(const QTextBlock &block);

protected:
    virtual void documentChanged(int from, int charsRemoved, int charsAdded) = 0;
    virtual void resizeInlineObject(QTextInlineObject item, int posInDocument, const QTextFormat &format);
    virtual void positionInlineObject(QTextInlineObject item, int posInDocument, const QTextFormat &format);
    virtual void drawInlineObject(QPainter *painter, const QRectF &rect, QTextInlineObject object, int posInDocument, const QTextFormat &format);
    QTextCharFormat format(int pos);

public:
%If (Qt_5_8_0 -)
    QString imageAt(const QPointF &pos) const;
%End
%If (Qt_5_8_0 -)
    QTextFormat formatAt(const QPointF &pos) const;
%End
%If (Qt_5_14_0 -)
    QTextBlock blockWithMarkerAt(const QPointF &pos) const;
%End
};

class QTextObjectInterface /Mixin,PyQtInterface="org.qt-project.Qt.QTextObjectInterface"/
{
%TypeHeaderCode
#include <qabstracttextdocumentlayout.h>
%End

public:
    virtual ~QTextObjectInterface();
    virtual QSizeF intrinsicSize(QTextDocument *doc, int posInDocument, const QTextFormat &format) = 0;
    virtual void drawObject(QPainter *painter, const QRectF &rect, QTextDocument *doc, int posInDocument, const QTextFormat &format) = 0;
};
