#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لنظام التراخيص الجديد
"""

import sys
import os
from datetime import datetime, timedelta
import hashlib

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_license_code_generation():
    """اختبار إنشاء كود الترخيص"""
    print("🔑 اختبار إنشاء كود الترخيص")
    print("=" * 40)
    
    try:
        from license_manager import LicenseManager
        
        lm = LicenseManager()
        customer_code = lm.get_customer_code()
        machine_id = lm.get_machine_id()
        
        print(f"🔑 كود العميل: {customer_code}")
        print(f"💻 رقم الجهاز: {machine_id}")
        
        # محاكاة إنشاء كود ترخيص (نفس الطريقة في برنامج المطور)
        expiry_date = datetime.now() + timedelta(days=365)
        date_str = expiry_date.strftime("%Y%m%d")
        
        # إنشاء hash للتحقق
        data_to_hash = f"{customer_code}{machine_id}{date_str}SICOO_LICENSE_2024"
        hash_value = hashlib.sha256(data_to_hash.encode()).hexdigest()[:8].upper()
        
        # تنسيق الكود النهائي
        license_code = f"SICOO-{date_str}-{customer_code[:6]}-{machine_id[:6]}-{hash_value}"
        
        print(f"📅 تاريخ الانتهاء: {expiry_date.strftime('%d/%m/%Y')}")
        print(f"🔐 كود الترخيص المُنشأ: {license_code}")
        
        return license_code, customer_code, machine_id
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء كود الترخيص: {e}")
        return None, None, None

def test_license_validation(license_code, customer_code, machine_id):
    """اختبار التحقق من كود الترخيص"""
    print(f"\n🧪 اختبار التحقق من كود الترخيص")
    print("=" * 40)
    
    try:
        from license_ui import LicenseDialog
        
        # إنشاء مثيل من LicenseDialog
        dialog = LicenseDialog()
        
        # اختبار التحقق من الكود
        result = dialog.validate_license_code(license_code, customer_code, machine_id)
        
        print(f"📊 نتيجة التحقق:")
        print(f"   - صالح: {result['valid']}")
        print(f"   - الرسالة: {result['message']}")
        
        if result["valid"]:
            print(f"   - تاريخ الانتهاء: {result['expiry_date'].strftime('%d/%m/%Y')}")
            print(f"   - الأيام المتبقية: {result['days_remaining']}")
            print("✅ التحقق من الكود نجح!")
            return result
        else:
            print("❌ التحقق من الكود فشل!")
            return None
            
    except Exception as e:
        print(f"❌ خطأ في التحقق من الكود: {e}")
        return None

def test_license_saving(license_data):
    """اختبار حفظ الترخيص"""
    print(f"\n💾 اختبار حفظ الترخيص")
    print("=" * 40)
    
    try:
        from license_ui import LicenseDialog
        from license_manager import LicenseManager
        
        # إنشاء مثيل من LicenseDialog
        dialog = LicenseDialog()
        
        # حفظ الترخيص
        if dialog.save_license(license_data):
            print("✅ تم حفظ الترخيص بنجاح!")
            
            # التحقق من الحفظ
            lm = LicenseManager()
            status = lm.check_license()
            
            print(f"📊 حالة الترخيص بعد الحفظ:")
            print(f"   - صالح: {status['valid']}")
            print(f"   - الحالة: {status['status']}")
            print(f"   - الرسالة: {status['message']}")
            
            if status["valid"]:
                print(f"   - تاريخ الانتهاء: {status['expiry_date'].strftime('%d/%m/%Y')}")
                print(f"   - الأيام المتبقية: {status['days_remaining']}")
                return True
            else:
                print("❌ الترخيص المحفوظ غير صالح!")
                return False
        else:
            print("❌ فشل في حفظ الترخيص!")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في حفظ الترخيص: {e}")
        return False

def test_invalid_codes():
    """اختبار أكواد ترخيص خاطئة"""
    print(f"\n🚫 اختبار أكواد ترخيص خاطئة")
    print("=" * 40)
    
    try:
        from license_ui import LicenseDialog
        from license_manager import LicenseManager
        
        lm = LicenseManager()
        customer_code = lm.get_customer_code()
        machine_id = lm.get_machine_id()
        
        dialog = LicenseDialog()
        
        # اختبار أكواد خاطئة مختلفة
        invalid_codes = [
            "INVALID-CODE",
            "SICOO-20200101-WRONG-WRONG-WRONG",
            "SICOO-INVALID-DATE-WRONG-WRONG",
            "",
            "SICOO-20991231-" + customer_code[:6] + "-" + machine_id[:6] + "-WRONGHASH"
        ]
        
        for i, code in enumerate(invalid_codes, 1):
            print(f"\n{i}. اختبار كود خاطئ: {code[:30]}...")
            result = dialog.validate_license_code(code, customer_code, machine_id)
            
            if not result["valid"]:
                print(f"   ✅ تم رفض الكود الخاطئ: {result['message']}")
            else:
                print(f"   ❌ خطأ: تم قبول كود خاطئ!")
                return False
        
        print(f"\n✅ جميع الأكواد الخاطئة تم رفضها بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الأكواد الخاطئة: {e}")
        return False

def test_application_protection():
    """اختبار حماية التطبيق"""
    print(f"\n🛡️ اختبار حماية التطبيق")
    print("=" * 40)
    
    try:
        from license_ui import check_license_and_show_dialog
        from license_manager import LicenseManager
        
        lm = LicenseManager()
        status = lm.check_license()
        
        print(f"📊 حالة الترخيص الحالية:")
        print(f"   - صالح: {status['valid']}")
        print(f"   - الحالة: {status['status']}")
        
        # محاكاة فحص الترخيص (بدون عرض النافذة)
        if status["valid"]:
            print("✅ البرنامج سيعمل بشكل طبيعي")
            return True
        else:
            print("✅ البرنامج سيعرض نافذة التفعيل")
            print("✅ نظام الحماية يعمل بشكل صحيح")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في اختبار حماية التطبيق: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔒 اختبار شامل لنظام التراخيص الجديد")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 5
    
    # اختبار 1: إنشاء كود الترخيص
    print("\n1️⃣ اختبار إنشاء كود الترخيص:")
    license_code, customer_code, machine_id = test_license_code_generation()
    if license_code:
        tests_passed += 1
        print("✅ نجح الاختبار")
    else:
        print("❌ فشل الاختبار")
        return
    
    # اختبار 2: التحقق من كود الترخيص
    print("\n2️⃣ اختبار التحقق من كود الترخيص:")
    validation_result = test_license_validation(license_code, customer_code, machine_id)
    if validation_result:
        tests_passed += 1
        print("✅ نجح الاختبار")
    else:
        print("❌ فشل الاختبار")
        return
    
    # اختبار 3: حفظ الترخيص
    print("\n3️⃣ اختبار حفظ الترخيص:")
    if test_license_saving(validation_result["license_data"]):
        tests_passed += 1
        print("✅ نجح الاختبار")
    else:
        print("❌ فشل الاختبار")
    
    # اختبار 4: أكواد خاطئة
    print("\n4️⃣ اختبار أكواد خاطئة:")
    if test_invalid_codes():
        tests_passed += 1
        print("✅ نجح الاختبار")
    else:
        print("❌ فشل الاختبار")
    
    # اختبار 5: حماية التطبيق
    print("\n5️⃣ اختبار حماية التطبيق:")
    if test_application_protection():
        tests_passed += 1
        print("✅ نجح الاختبار")
    else:
        print("❌ فشل الاختبار")
    
    # النتيجة النهائية
    print(f"\n" + "=" * 60)
    print(f"📊 النتيجة النهائية: {tests_passed}/{total_tests} اختبارات نجحت")
    
    if tests_passed == total_tests:
        print("🎉 جميع الاختبارات نجحت - نظام التراخيص الجديد يعمل بشكل مثالي!")
        print("\n🚀 النظام جاهز للاستخدام:")
        print("   • برنامج إصدار التراخيص: python license_generator_app.py")
        print("   • البرنامج الأصلي: python main.py")
    else:
        print("⚠️ بعض الاختبارات فشلت - يحتاج النظام إلى مراجعة")
    
    return tests_passed == total_tests

if __name__ == "__main__":
    main()
