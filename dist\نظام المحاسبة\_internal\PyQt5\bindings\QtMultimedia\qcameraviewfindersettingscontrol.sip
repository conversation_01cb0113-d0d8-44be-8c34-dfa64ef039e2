// qcameraviewfindersettingscontrol.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QCameraViewfinderSettingsControl : public QMediaControl
{
%TypeHeaderCode
#include <qcameraviewfindersettingscontrol.h>
%End

public:
    enum ViewfinderParameter
    {
        Resolution,
        PixelAspectRatio,
        MinimumFrameRate,
        MaximumFrameRate,
        PixelFormat,
        UserParameter,
    };

    virtual ~QCameraViewfinderSettingsControl();
    virtual bool isViewfinderParameterSupported(QCameraViewfinderSettingsControl::ViewfinderParameter parameter) const = 0;
    virtual QVariant viewfinderParameter(QCameraViewfinderSettingsControl::ViewfinderParameter parameter) const = 0;
    virtual void setViewfinderParameter(QCameraViewfinderSettingsControl::ViewfinderParameter parameter, const QVariant &value) = 0;

protected:
    explicit QCameraViewfinderSettingsControl(QObject *parent /TransferThis/ = 0);
};

class QCameraViewfinderSettingsControl2 : public QMediaControl
{
%TypeHeaderCode
#include <qcameraviewfindersettingscontrol.h>
%End

public:
    virtual ~QCameraViewfinderSettingsControl2();
    virtual QList<QCameraViewfinderSettings> supportedViewfinderSettings() const = 0;
    virtual QCameraViewfinderSettings viewfinderSettings() const = 0;
    virtual void setViewfinderSettings(const QCameraViewfinderSettings &settings) = 0;

protected:
    explicit QCameraViewfinderSettingsControl2(QObject *parent /TransferThis/ = 0);
};
