#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار فرض نظام الترخيص - التأكد من أن البرنامج لا يعمل بدون ترخيص صالح
"""

import sys
import os
from datetime import datetime, timedelta

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_license_enforcement():
    """اختبار فرض نظام الترخيص"""
    print("🔐 اختبار فرض نظام الترخيص")
    print("=" * 50)
    
    try:
        from license_manager import LicenseManager
        from license_ui import check_license_and_show_dialog
        
        # إنشاء مدير التراخيص
        lm = LicenseManager()
        
        # عرض معلومات الجهاز
        customer_code = lm.get_customer_code()
        machine_id = lm.get_machine_id()
        
        print(f"🔑 كود العميل: {customer_code}")
        print(f"💻 رقم الجهاز: {machine_id}")
        
        # فحص الترخيص الحالي
        status = lm.check_license()
        
        print(f"\n📊 حالة الترخيص الحالية:")
        print(f"   - صالح: {status['valid']}")
        print(f"   - الحالة: {status['status']}")
        print(f"   - الرسالة: {status['message']}")
        
        if status["valid"]:
            print(f"   - ينتهي في: {status['expiry_date'].strftime('%d/%m/%Y')}")
            print(f"   - متبقي: {status['days_remaining']} يوم")
        
        # اختبار دالة فحص الترخيص والحوار
        print(f"\n🧪 اختبار دالة check_license_and_show_dialog:")
        
        # محاكاة فحص الترخيص (بدون عرض النافذة فعلياً)
        if status["valid"]:
            print("✅ الترخيص صالح - سيتم السماح بتشغيل البرنامج")
            return True
        else:
            print("❌ الترخيص غير صالح - سيتم عرض نافذة التفعيل")
            print("🚫 البرنامج لن يعمل حتى يتم التفعيل")
            return False
            
    except ImportError as e:
        print(f"❌ خطأ في استيراد نظام التراخيص: {e}")
        print("⚠️ نظام التراخيص غير متوفر")
        return False
    except Exception as e:
        print(f"❌ خطأ في اختبار نظام الترخيص: {e}")
        return False

def simulate_expired_license():
    """محاكاة ترخيص منتهي الصلاحية"""
    print("\n🕐 محاكاة ترخيص منتهي الصلاحية...")
    
    try:
        from license_manager import LicenseManager
        
        lm = LicenseManager()
        
        # إنشاء ترخيص منتهي الصلاحية (أمس)
        expired_date = datetime.now() - timedelta(days=1)
        expired_license = {
            "customer_code": lm.get_customer_code(),
            "machine_id": lm.get_machine_id(),
            "expiry_date": expired_date.isoformat(),
            "license_type": "EXPIRED_TEST",
            "created_date": datetime.now().isoformat()
        }
        
        # حفظ الترخيص المنتهي
        if lm._save_license(expired_license):
            print("✅ تم إنشاء ترخيص منتهي الصلاحية للاختبار")
            
            # فحص الترخيص
            status = lm.check_license()
            
            print(f"📊 نتيجة الفحص:")
            print(f"   - صالح: {status['valid']}")
            print(f"   - الحالة: {status['status']}")
            print(f"   - الرسالة: {status['message']}")
            
            if not status["valid"]:
                print("✅ نظام الترخيص يعمل بشكل صحيح - تم رفض الترخيص المنتهي")
                return True
            else:
                print("❌ خطأ: نظام الترخيص قبل ترخيص منتهي الصلاحية!")
                return False
        else:
            print("❌ فشل في حفظ الترخيص المنتهي للاختبار")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في محاكاة الترخيص المنتهي: {e}")
        return False

def test_main_application_protection():
    """اختبار حماية التطبيق الرئيسي"""
    print("\n🛡️ اختبار حماية التطبيق الرئيسي...")
    
    try:
        # فحص إذا كان main.py يحتوي على فحص الترخيص
        with open('main.py', 'r', encoding='utf-8') as f:
            main_content = f.read()
        
        if 'check_license_and_show_dialog' in main_content:
            print("✅ main.py يحتوي على فحص الترخيص")
        else:
            print("❌ main.py لا يحتوي على فحص الترخيص!")
            return False
        
        if 'LICENSE_SYSTEM_AVAILABLE' in main_content:
            print("✅ main.py يتحقق من توفر نظام التراخيص")
        else:
            print("⚠️ main.py لا يتحقق من توفر نظام التراخيص")
        
        # فحص إذا كان البرنامج يتوقف عند فشل الترخيص
        if 'return 1' in main_content and 'لم يتم تفعيل الترخيص' in main_content:
            print("✅ البرنامج يتوقف عند فشل التفعيل")
            return True
        else:
            print("❌ البرنامج لا يتوقف عند فشل التفعيل!")
            return False
            
    except FileNotFoundError:
        print("❌ لم يتم العثور على ملف main.py")
        return False
    except Exception as e:
        print(f"❌ خطأ في فحص حماية التطبيق: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔒 اختبار شامل لنظام فرض التراخيص")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 3
    
    # اختبار 1: فحص نظام الترخيص
    print("\n1️⃣ اختبار نظام الترخيص الأساسي:")
    if test_license_enforcement():
        tests_passed += 1
        print("✅ نجح الاختبار")
    else:
        print("❌ فشل الاختبار")
    
    # اختبار 2: محاكاة ترخيص منتهي
    print("\n2️⃣ اختبار الترخيص المنتهي:")
    if simulate_expired_license():
        tests_passed += 1
        print("✅ نجح الاختبار")
    else:
        print("❌ فشل الاختبار")
    
    # اختبار 3: حماية التطبيق الرئيسي
    print("\n3️⃣ اختبار حماية التطبيق الرئيسي:")
    if test_main_application_protection():
        tests_passed += 1
        print("✅ نجح الاختبار")
    else:
        print("❌ فشل الاختبار")
    
    # النتيجة النهائية
    print(f"\n" + "=" * 60)
    print(f"📊 النتيجة النهائية: {tests_passed}/{total_tests} اختبارات نجحت")
    
    if tests_passed == total_tests:
        print("🎉 جميع الاختبارات نجحت - نظام الترخيص يعمل بشكل صحيح!")
    else:
        print("⚠️ بعض الاختبارات فشلت - يحتاج نظام الترخيص إلى إصلاح")
    
    return tests_passed == total_tests

if __name__ == "__main__":
    main()
