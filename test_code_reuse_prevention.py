#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار منع إعادة استخدام أكواد الترخيص
"""

import sys
import os
from datetime import datetime, timedelta
import hashlib

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_code_reuse_prevention():
    """اختبار منع إعادة استخدام الأكواد"""
    print("🔒 اختبار منع إعادة استخدام أكواد الترخيص")
    print("=" * 60)
    
    try:
        from license_manager import LicenseManager

        # إنشاء مدير التراخيص
        lm = LicenseManager()

        # محاكاة دالة التحقق من الكود (بدون GUI)
        def validate_license_code_test(license_code, customer_code, machine_id):
            """محاكاة دالة التحقق من الكود"""
            # التحقق من استخدام الكود مسبقاً
            if lm.is_code_used(license_code):
                return {
                    "valid": False,
                    "message": "هذا الكود مستخدم مسبقاً ولا يمكن استخدامه مرة أخرى"
                }

            # تنسيق الكود المتوقع: SICOO-YYYYMMDD-CUSTOMER-MACHINE-HASH
            parts = license_code.strip().upper().split('-')

            if len(parts) != 5 or parts[0] != "SICOO":
                return {
                    "valid": False,
                    "message": "تنسيق كود الترخيص غير صحيح"
                }

            date_str = parts[1]
            customer_part = parts[2]
            machine_part = parts[3]
            hash_part = parts[4]

            # التحقق من التاريخ
            try:
                from datetime import datetime
                expiry_date = datetime.strptime(date_str, "%Y%m%d")
                current_date = datetime.now()

                if current_date > expiry_date:
                    return {
                        "valid": False,
                        "message": "كود الترخيص منتهي الصلاحية"
                    }

                days_remaining = (expiry_date - current_date).days

            except ValueError:
                return {
                    "valid": False,
                    "message": "تاريخ انتهاء الترخيص غير صحيح"
                }

            # التحقق من كود العميل ورقم الجهاز
            expected_customer = customer_code[:6].upper()
            expected_machine = machine_id[:6].upper()

            if customer_part != expected_customer:
                return {
                    "valid": False,
                    "message": "كود الترخيص غير صالح لهذا العميل"
                }

            if machine_part != expected_machine:
                return {
                    "valid": False,
                    "message": "كود الترخيص غير صالح لهذا الجهاز"
                }

            # التحقق من الـ hash
            data_to_hash = f"{customer_code}{machine_id}{date_str}SICOO_LICENSE_2024"
            expected_hash = hashlib.sha256(data_to_hash.encode()).hexdigest()[:8].upper()

            if hash_part != expected_hash:
                return {
                    "valid": False,
                    "message": "كود الترخيص غير صحيح أو تالف"
                }

            # إنشاء بيانات الترخيص
            license_data = {
                "customer_code": customer_code,
                "machine_id": machine_id,
                "expiry_date": expiry_date.isoformat(),
                "license_type": "FULL",
                "license_code": license_code,
                "activated_date": datetime.now().isoformat()
            }

            return {
                "valid": True,
                "message": "كود الترخيص صحيح",
                "expiry_date": expiry_date,
                "days_remaining": days_remaining,
                "license_data": license_data
            }
        
        # الحصول على بيانات الجهاز
        customer_code = lm.get_customer_code()
        machine_id = lm.get_machine_id()
        
        print(f"🔑 كود العميل: {customer_code}")
        print(f"💻 رقم الجهاز: {machine_id}")
        
        # إنشاء كود ترخيص للاختبار
        expiry_date = datetime.now() + timedelta(days=365)
        date_str = expiry_date.strftime("%Y%m%d")
        data_to_hash = f"{customer_code}{machine_id}{date_str}SICOO_LICENSE_2024"
        hash_value = hashlib.sha256(data_to_hash.encode()).hexdigest()[:8].upper()
        test_license_code = f"SICOO-{date_str}-{customer_code[:6]}-{machine_id[:6]}-{hash_value}"
        
        print(f"\n📝 كود الاختبار: {test_license_code}")
        
        # الاختبار الأول: استخدام الكود لأول مرة
        print(f"\n1️⃣ الاختبار الأول: استخدام الكود لأول مرة")
        
        # التحقق من أن الكود غير مستخدم
        is_used_before = lm.is_code_used(test_license_code)
        print(f"   - الكود مستخدم مسبقاً: {is_used_before}")
        
        if not is_used_before:
            print("   ✅ الكود غير مستخدم - يمكن استخدامه")
        else:
            print("   ⚠️ الكود مستخدم مسبقاً")
        
        # التحقق من صحة الكود
        validation_result = validate_license_code_test(test_license_code, customer_code, machine_id)

        if validation_result["valid"]:
            print("   ✅ التحقق من الكود نجح")

            # حفظ الترخيص (هذا سيسجل الكود كمستخدم)
            if lm._save_license(validation_result["license_data"]):
                print("   ✅ تم حفظ الترخيص وتسجيل الكود كمستخدم")
            else:
                print("   ❌ فشل في حفظ الترخيص")
                return False
        else:
            print(f"   ❌ التحقق من الكود فشل: {validation_result['message']}")
            return False
        
        # الاختبار الثاني: محاولة استخدام نفس الكود مرة أخرى
        print(f"\n2️⃣ الاختبار الثاني: محاولة استخدام نفس الكود مرة أخرى")
        
        # التحقق من أن الكود أصبح مستخدم
        is_used_after = lm.is_code_used(test_license_code)
        print(f"   - الكود مستخدم الآن: {is_used_after}")
        
        if is_used_after:
            print("   ✅ الكود مسجل كمستخدم")
        else:
            print("   ❌ الكود لم يُسجل كمستخدم!")
            return False
        
        # محاولة التحقق من الكود مرة أخرى
        validation_result_2 = validate_license_code_test(test_license_code, customer_code, machine_id)
        
        if not validation_result_2["valid"]:
            print(f"   ✅ تم رفض الكود المستخدم: {validation_result_2['message']}")
            
            # التحقق من أن الرسالة صحيحة
            if "مستخدم مسبقاً" in validation_result_2["message"]:
                print("   ✅ رسالة الخطأ صحيحة")
                return True
            else:
                print("   ⚠️ رسالة الخطأ غير متوقعة")
                return False
        else:
            print("   ❌ خطأ: تم قبول الكود المستخدم مسبقاً!")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_different_codes():
    """اختبار أكواد مختلفة"""
    print(f"\n🔄 اختبار أكواد مختلفة")
    print("=" * 30)
    
    try:
        from license_manager import LicenseManager

        lm = LicenseManager()
        customer_code = lm.get_customer_code()
        machine_id = lm.get_machine_id()

        # إنشاء كود مختلف (تاريخ مختلف)
        expiry_date = datetime.now() + timedelta(days=730)  # سنتين
        date_str = expiry_date.strftime("%Y%m%d")
        data_to_hash = f"{customer_code}{machine_id}{date_str}SICOO_LICENSE_2024"
        hash_value = hashlib.sha256(data_to_hash.encode()).hexdigest()[:8].upper()
        different_code = f"SICOO-{date_str}-{customer_code[:6]}-{machine_id[:6]}-{hash_value}"

        print(f"📝 كود مختلف: {different_code}")

        # التحقق من أن الكود الجديد غير مستخدم
        is_used = lm.is_code_used(different_code)
        print(f"   - الكود الجديد مستخدم: {is_used}")

        if not is_used:
            print("   ✅ الكود الجديد غير مستخدم - يمكن استخدامه")
            print("   ✅ الكود الجديد صالح ويمكن استخدامه")
            return True
        else:
            print("   ⚠️ الكود الجديد مستخدم مسبقاً")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الأكواد المختلفة: {e}")
        return False

def test_used_codes_persistence():
    """اختبار استمرارية حفظ الأكواد المستخدمة"""
    print(f"\n💾 اختبار استمرارية حفظ الأكواد المستخدمة")
    print("=" * 50)
    
    try:
        from license_manager import LicenseManager
        
        # إنشاء مدير تراخيص جديد
        lm1 = LicenseManager()
        
        # إضافة كود وهمي للاختبار
        test_code = "SICOO-20251231-TEST01-TEST02-ABCD1234"
        lm1.mark_code_as_used(test_code)
        
        print(f"📝 تم تسجيل كود اختبار: {test_code}")
        
        # إنشاء مدير تراخيص جديد (محاكاة إعادة تشغيل البرنامج)
        lm2 = LicenseManager()
        
        # التحقق من أن الكود ما زال مسجل كمستخدم
        is_still_used = lm2.is_code_used(test_code)
        
        if is_still_used:
            print("   ✅ الكود ما زال مسجل كمستخدم بعد إعادة التشغيل")
            return True
        else:
            print("   ❌ الكود لم يعد مسجل كمستخدم!")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الاستمرارية: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔐 اختبار شامل لمنع إعادة استخدام أكواد الترخيص")
    print("=" * 70)
    
    tests_passed = 0
    total_tests = 3
    
    # اختبار 1: منع إعادة الاستخدام
    if test_code_reuse_prevention():
        tests_passed += 1
        print("✅ اختبار منع إعادة الاستخدام نجح")
    else:
        print("❌ اختبار منع إعادة الاستخدام فشل")
    
    # اختبار 2: أكواد مختلفة
    if test_different_codes():
        tests_passed += 1
        print("✅ اختبار الأكواد المختلفة نجح")
    else:
        print("❌ اختبار الأكواد المختلفة فشل")
    
    # اختبار 3: استمرارية الحفظ
    if test_used_codes_persistence():
        tests_passed += 1
        print("✅ اختبار الاستمرارية نجح")
    else:
        print("❌ اختبار الاستمرارية فشل")
    
    # النتيجة النهائية
    print(f"\n" + "=" * 70)
    print(f"📊 النتيجة النهائية: {tests_passed}/{total_tests} اختبارات نجحت")
    
    if tests_passed == total_tests:
        print("🎉 جميع الاختبارات نجحت - نظام منع إعادة الاستخدام يعمل بشكل مثالي!")
        print("\n📋 الخلاصة:")
        print("   ✅ لا يمكن استخدام نفس الكود أكثر من مرة")
        print("   ✅ يمكن استخدام أكواد مختلفة")
        print("   ✅ الأكواد المستخدمة محفوظة بشكل دائم")
    else:
        print("⚠️ بعض الاختبارات فشلت - يحتاج النظام إلى مراجعة")
    
    return tests_passed == total_tests

if __name__ == "__main__":
    main()
