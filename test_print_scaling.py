#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تطابق نسب الطباعة مع المعاينة
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox, QLabel
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.users import init_db
from utils.advanced_invoice_printer import show_advanced_print_dialog

class TestScalingWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار تطابق نسب الطباعة مع المعاينة")
        self.setGeometry(100, 100, 500, 300)
        
        # إعداد قاعدة البيانات
        self.engine = create_engine('sqlite:///accounting.db', echo=False)
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # عنوان
        title = QLabel("🎯 اختبار تطابق نسب الطباعة")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                background-color: #3498db;
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # تعليمات
        instructions = QLabel("""
📋 التحديثات المطبقة:
• توحيد أبعاد المعاينة والطباعة
• استخدام نسبة A4 الصحيحة (794x1123)
• إزالة الهوامش لملء الصفحة بالكامل
• رسم الفاتورة لتملأ الصفحة مثل المعاينة

🎯 ما يجب اختباره:
• افتح نافذة الطباعة
• لاحظ حجم الفاتورة في المعاينة
• اطبع أو احفظ PDF
• تأكد أن الحجم متطابق تماماً
        """)
        instructions.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                font-size: 12px;
                line-height: 1.5;
            }
        """)
        layout.addWidget(instructions)
        
        # زر اختبار الطباعة
        test_btn = QPushButton("🖨️ اختبار طباعة آخر فاتورة")
        test_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 15px;
                font-size: 16px;
                font-weight: bold;
                border-radius: 8px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        test_btn.clicked.connect(self.test_print_last_invoice)
        
        layout.addWidget(test_btn)
        
        # زر معلومات التحديث
        info_btn = QPushButton("ℹ️ معلومات التحديث")
        info_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                padding: 10px;
                font-size: 14px;
                border-radius: 8px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        info_btn.clicked.connect(self.show_update_info)
        
        layout.addWidget(info_btn)
        
    def test_print_last_invoice(self):
        """اختبار طباعة آخر فاتورة"""
        try:
            from sqlalchemy.orm import Session
            from database.models import Transaction, TransactionType
            
            with Session(self.engine) as session:
                # البحث عن آخر فاتورة مبيعات
                last_invoice = session.query(Transaction).filter(
                    Transaction.type == TransactionType.SALE,
                    Transaction.status != 'draft'  # استثناء المسودات
                ).order_by(Transaction.id.desc()).first()
                
                if last_invoice:
                    print(f"✅ تم العثور على الفاتورة رقم {last_invoice.id}")
                    print("🖨️ فتح نافذة الطباعة المحدثة...")
                    
                    # فتح نافذة الطباعة المحدثة
                    show_advanced_print_dialog(self.engine, last_invoice.id, self)
                    
                    QMessageBox.information(
                        self, 
                        "✅ تم فتح نافذة الطباعة المحدثة", 
                        f"تم فتح نافذة طباعة الفاتورة رقم {last_invoice.id:06d}\n\n"
                        "🎯 التحديثات المطبقة:\n"
                        "• نسب المعاينة والطباعة متطابقة\n"
                        "• أبعاد A4 صحيحة (794x1123)\n"
                        "• بدون هوامش لملء الصفحة\n"
                        "• رسم يملأ الصفحة بالكامل\n\n"
                        "📋 اختبر الآن:\n"
                        "• لاحظ حجم الفاتورة في المعاينة\n"
                        "• اطبع أو احفظ PDF\n"
                        "• تأكد من تطابق الأحجام"
                    )
                else:
                    QMessageBox.warning(
                        self, 
                        "⚠️ لا توجد فواتير", 
                        "لم يتم العثور على أي فواتير في النظام.\n\n"
                        "💡 نصيحة:\n"
                        "قم بإنشاء فاتورة مبيعات أولاً من خلال:\n"
                        "المبيعات → إضافة فاتورة جديدة"
                    )
                    
        except Exception as e:
            QMessageBox.critical(
                self, 
                "❌ خطأ", 
                f"حدث خطأ أثناء البحث عن الفواتير:\n{str(e)}"
            )
    
    def show_update_info(self):
        """عرض معلومات التحديث"""
        QMessageBox.information(
            self,
            "ℹ️ معلومات التحديث",
            """🔧 التحديثات المطبقة على نظام الطباعة:

📐 توحيد الأبعاد:
• المعاينة: 794x1123 (نسبة A4 الصحيحة)
• الطباعة: 794x1123 × 3 (نفس النسبة مع جودة أعلى)
• PDF: 794x1123 × 3 (نفس النسبة مع جودة أعلى)

📄 إعدادات الصفحة:
• إزالة الهوامش (0mm من كل الجهات)
• ملء الصفحة بالكامل
• رسم مباشر بدون تكبير أو تصغير

🎯 النتيجة المتوقعة:
• المعاينة والطباعة متطابقتان تماماً
• الفاتورة تملأ ورقة A4 بالكامل
• نفس الحجم والنسب في كل الحالات

✅ تم حل مشكلة اختلاف الأحجام!"""
        )

def main():
    print("🎯 اختبار تطابق نسب الطباعة مع المعاينة...")
    print("=" * 60)
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء النافذة الرئيسية
    window = TestScalingWindow()
    window.show()
    
    print("✅ تم تشغيل نافذة الاختبار!")
    print("")
    print("🔧 التحديثات المطبقة:")
    print("   ✅ توحيد أبعاد المعاينة والطباعة")
    print("   ✅ استخدام نسبة A4 الصحيحة")
    print("   ✅ إزالة الهوامش لملء الصفحة")
    print("   ✅ رسم مباشر بدون تكبير")
    print("")
    print("🎯 ما يجب اختباره:")
    print("   1. افتح نافذة الطباعة")
    print("   2. لاحظ حجم الفاتورة في المعاينة")
    print("   3. اطبع أو احفظ PDF")
    print("   4. تأكد من تطابق الأحجام تماماً")
    print("")
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
