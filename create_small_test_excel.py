#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملف Excel صغير لاختبار الحل الجديد
"""

import pandas as pd
from datetime import datetime

def create_small_test_excel():
    """إنشاء ملف Excel صغير للاختبار"""
    
    # بيانات تجريبية صغيرة
    products_data = [
        {
            'اسم المنتج': 'منتج اختبار جديد 1',
            'الكود': 'TEST_NEW_001',
            'سعر الشراء': 100.0,
            'سعر البيع': 150.0,
            'الكمية': 10,
            'الوحدة': 'قطعة',
            'القيمه': 1500.0,
            'المكسب': 500.0
        },
        {
            'اسم المنتج': 'منتج اختبار جديد 2',
            'الكود': 'TEST_NEW_002',
            'سعر الشراء': 200.0,
            'سعر البيع': 300.0,
            'الكمية': 5,
            'الوحدة': 'قطعة',
            'القيمه': 1500.0,
            'المكسب': 500.0
        },
        {
            'اسم المنتج': 'منتج اختبار جديد 3',
            'الكود': 'TEST_NEW_003',
            'سعر الشراء': 50.0,
            'سعر البيع': 75.0,
            'الكمية': 20,
            'الوحدة': 'قطعة',
            'القيمه': 1500.0,
            'المكسب': 500.0
        },
        {
            'اسم المنتج': 'منتج بدون كود جديد',
            'الكود': '',
            'سعر الشراء': 30.0,
            'سعر البيع': 45.0,
            'الكمية': 15,
            'الوحدة': 'قطعة',
            'القيمه': 675.0,
            'المكسب': 225.0
        },
        {
            'اسم المنتج': 'منتج آخر بدون كود جديد',
            'الكود': '',
            'سعر الشراء': 40.0,
            'سعر البيع': 60.0,
            'الكمية': 8,
            'الوحدة': 'قطعة',
            'القيمه': 480.0,
            'المكسب': 160.0
        }
    ]
    
    # إنشاء DataFrame
    df = pd.DataFrame(products_data)
    
    # حفظ في ملف Excel
    filename = f"test_new_products_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    df.to_excel(filename, index=False, engine='openpyxl')
    
    print(f"✅ تم إنشاء ملف Excel للاختبار: {filename}")
    print(f"📊 عدد المنتجات: {len(products_data)}")
    print("\n📋 المنتجات:")
    for i, product in enumerate(products_data, 1):
        code_display = product['الكود'] if product['الكود'] else '[فارغ]'
        print(f"  {i}. {product['اسم المنتج']} - كود: {code_display}")
    
    print(f"\n🎯 هذا الملف سيختبر:")
    print("  • إضافة منتجات جديدة تماماً")
    print("  • التعامل مع الأكواد الفارغة")
    print("  • الخيار الجديد: 'إضافة جميع المنتجات (تعطيل فحص التكرار)'")
    
    return filename

if __name__ == "__main__":
    create_small_test_excel()
