#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نافذة تفعيل مبسطة لحل مشكلة عدم ظهور حقل الإدخال
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QDialog, QVBoxLayout, QHBoxLayout, 
                             QLabel, QLineEdit, QPushButton, QTextEdit, QFrame)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class SimpleActivationDialog(QDialog):
    """نافذة تفعيل مبسطة لاختبار المشكلة"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔑 تفعيل البرنامج - نسخة مبسطة")
        self.setFixedSize(600, 500)
        self.setModal(True)
        
        # استيراد مدير التراخيص
        from license_manager import LicenseManager
        self.license_manager = LicenseManager()
        
        self.setup_ui()
        self.load_status()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        
        # العنوان
        title = QLabel("🔒 البرنامج غير مفعل")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #e74c3c; margin: 20px;")
        layout.addWidget(title)
        
        # معلومات الجهاز
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                margin: 10px;
            }
        """)
        info_layout = QVBoxLayout()
        
        customer_code = self.license_manager.get_customer_code()
        machine_id = self.license_manager.get_machine_id()
        
        info_layout.addWidget(QLabel(f"🔑 كود العميل: {customer_code}"))
        info_layout.addWidget(QLabel(f"💻 رقم الجهاز: {machine_id}"))
        
        info_frame.setLayout(info_layout)
        layout.addWidget(info_frame)
        
        # تعليمات
        instructions = QLabel("""
📞 للحصول على كود التفعيل:
1️⃣ أرسل البيانات أعلاه إلى المطور
2️⃣ البريد الإلكتروني: <EMAIL>
3️⃣ ستحصل على كود التفعيل خلال 24 ساعة
4️⃣ أدخل الكود في الحقل أدناه واضغط "تفعيل"
        """)
        instructions.setStyleSheet("color: #1976d2; margin: 10px; padding: 10px;")
        layout.addWidget(instructions)
        
        # حقل كود التفعيل - مبسط
        activation_layout = QHBoxLayout()
        
        activation_label = QLabel("🔑 كود التفعيل:")
        activation_label.setMinimumWidth(100)
        activation_layout.addWidget(activation_label)
        
        self.activation_code = QLineEdit()
        self.activation_code.setPlaceholderText("أدخل كود التفعيل هنا...")
        self.activation_code.setMinimumHeight(40)
        self.activation_code.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                border: 2px solid #3498db;
                border-radius: 5px;
                font-size: 14px;
                font-family: monospace;
            }
        """)
        activation_layout.addWidget(self.activation_code)
        
        layout.addLayout(activation_layout)
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        
        copy_btn = QPushButton("📋 نسخ بيانات الجهاز")
        copy_btn.clicked.connect(self.copy_machine_data)
        copy_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        buttons_layout.addWidget(copy_btn)
        
        activate_btn = QPushButton("✅ تفعيل البرنامج")
        activate_btn.clicked.connect(self.activate_license)
        activate_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        buttons_layout.addWidget(activate_btn)
        
        layout.addLayout(buttons_layout)
        
        self.setLayout(layout)
        
        # التأكد من أن حقل الإدخال مرئي
        print(f"🔧 حقل الإدخال مرئي: {self.activation_code.isVisible()}")
        print(f"🔧 حقل الإدخال مفعل: {self.activation_code.isEnabled()}")
    
    def load_status(self):
        """تحميل حالة الترخيص"""
        status = self.license_manager.check_license()
        print(f"📊 حالة الترخيص: {status}")
    
    def copy_machine_data(self):
        """نسخ بيانات الجهاز"""
        customer_code = self.license_manager.get_customer_code()
        machine_id = self.license_manager.get_machine_id()
        
        data = f"""
بيانات الجهاز للتفعيل:

🔑 كود العميل: {customer_code}
💻 رقم الجهاز: {machine_id}

يرجى إرسال هذه البيانات إلى:
📧 <EMAIL>

للحصول على كود التفعيل.
        """
        
        from PyQt5.QtWidgets import QApplication
        QApplication.clipboard().setText(data.strip())
        
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(self, "تم النسخ", "تم نسخ بيانات الجهاز إلى الحافظة")
    
    def activate_license(self):
        """تفعيل البرنامج"""
        code = self.activation_code.text().strip()
        
        if not code:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "تنبيه", "يرجى إدخال كود التفعيل")
            return
        
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(self, "اختبار", f"تم إدخال الكود: {code}")

def test_simple_activation():
    """اختبار النافذة المبسطة"""
    print("🔧 اختبار نافذة التفعيل المبسطة")
    print("=" * 50)
    
    # حذف ملفات التراخيص
    license_files = ["license.dat", "used_codes.dat"]
    for file in license_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"✅ تم حذف {file}")
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    app.setLayoutDirection(2)  # RTL
    
    # إنشاء النافذة
    dialog = SimpleActivationDialog()
    
    print(f"✅ تم إنشاء النافذة المبسطة")
    print(f"📏 حجم النافذة: {dialog.size().width()} x {dialog.size().height()}")
    
    # فحص حقل الإدخال
    print(f"✅ حقل الإدخال موجود: {hasattr(dialog, 'activation_code')}")
    if hasattr(dialog, 'activation_code'):
        print(f"👁️ مرئي: {dialog.activation_code.isVisible()}")
        print(f"🔧 مفعل: {dialog.activation_code.isEnabled()}")
        print(f"📝 النص التوضيحي: {dialog.activation_code.placeholderText()}")
    
    print(f"\n🖥️ عرض النافذة...")
    print(f"   📋 تحقق من ظهور حقل 'كود التفعيل'")
    print(f"   (اضغط Ctrl+C لإغلاق النافذة)")
    
    dialog.show()
    
    return app.exec_()

if __name__ == "__main__":
    try:
        result = test_simple_activation()
        print(f"\n✅ تم إغلاق النافذة بالكود: {result}")
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
