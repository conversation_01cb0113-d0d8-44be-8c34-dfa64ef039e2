#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج إصدار التراخيص للمطور
"""

import sys
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import hashlib
from datetime import datetime, timedelta
import json
import os

class LicenseGeneratorApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔑 برنامج إصدار التراخيص - للمطور فقط")
        self.root.geometry("800x600")
        self.root.configure(bg='#2c3e50')
        
        # قاعدة بيانات التراخيص المصدرة
        self.licenses_db_file = "issued_licenses.json"
        self.issued_licenses = self.load_issued_licenses()
        
        self.setup_ui()
        
    def load_issued_licenses(self):
        """تحميل قاعدة بيانات التراخيص المصدرة"""
        try:
            if os.path.exists(self.licenses_db_file):
                with open(self.licenses_db_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except:
            pass
        return {}
    
    def save_issued_licenses(self):
        """حفظ قاعدة بيانات التراخيص المصدرة"""
        try:
            with open(self.licenses_db_file, 'w', encoding='utf-8') as f:
                json.dump(self.issued_licenses, f, ensure_ascii=False, indent=2)
            return True
        except:
            return False
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان الرئيسي
        title_frame = tk.Frame(self.root, bg='#2c3e50')
        title_frame.pack(pady=20)
        
        title_label = tk.Label(
            title_frame,
            text="🔑 برنامج إصدار التراخيص",
            font=("Arial", 20, "bold"),
            bg='#2c3e50',
            fg='#ecf0f1'
        )
        title_label.pack()
        
        subtitle_label = tk.Label(
            title_frame,
            text="للمطور فقط - إصدار وإدارة تراخيص العملاء",
            font=("Arial", 12),
            bg='#2c3e50',
            fg='#bdc3c7'
        )
        subtitle_label.pack()
        
        # إطار إصدار ترخيص جديد
        self.create_new_license_frame()
        
        # إطار عرض التراخيص المصدرة
        self.create_licenses_list_frame()
        
    def create_new_license_frame(self):
        """إطار إصدار ترخيص جديد"""
        new_license_frame = tk.LabelFrame(
            self.root,
            text="📝 إصدار ترخيص جديد",
            font=("Arial", 14, "bold"),
            bg='#34495e',
            fg='#ecf0f1',
            padx=20,
            pady=15
        )
        new_license_frame.pack(pady=10, padx=20, fill='x')
        
        # بيانات العميل
        customer_frame = tk.Frame(new_license_frame, bg='#34495e')
        customer_frame.pack(fill='x', pady=5)
        
        tk.Label(
            customer_frame,
            text="👤 اسم العميل:",
            font=("Arial", 11, "bold"),
            bg='#34495e',
            fg='#ecf0f1'
        ).pack(side='left')
        
        self.customer_name_entry = tk.Entry(
            customer_frame,
            font=("Arial", 11),
            width=30
        )
        self.customer_name_entry.pack(side='left', padx=(10, 0))
        
        # كود العميل
        customer_code_frame = tk.Frame(new_license_frame, bg='#34495e')
        customer_code_frame.pack(fill='x', pady=5)
        
        tk.Label(
            customer_code_frame,
            text="🔑 كود العميل:",
            font=("Arial", 11, "bold"),
            bg='#34495e',
            fg='#ecf0f1'
        ).pack(side='left')
        
        self.customer_code_entry = tk.Entry(
            customer_code_frame,
            font=("Arial", 11),
            width=20
        )
        self.customer_code_entry.pack(side='left', padx=(10, 0))
        
        # رقم الجهاز
        machine_frame = tk.Frame(new_license_frame, bg='#34495e')
        machine_frame.pack(fill='x', pady=5)
        
        tk.Label(
            machine_frame,
            text="💻 رقم الجهاز:",
            font=("Arial", 11, "bold"),
            bg='#34495e',
            fg='#ecf0f1'
        ).pack(side='left')
        
        self.machine_id_entry = tk.Entry(
            machine_frame,
            font=("Arial", 11),
            width=30
        )
        self.machine_id_entry.pack(side='left', padx=(10, 0))
        
        # مدة الترخيص
        duration_frame = tk.Frame(new_license_frame, bg='#34495e')
        duration_frame.pack(fill='x', pady=5)
        
        tk.Label(
            duration_frame,
            text="⏰ مدة الترخيص (أيام):",
            font=("Arial", 11, "bold"),
            bg='#34495e',
            fg='#ecf0f1'
        ).pack(side='left')
        
        self.duration_entry = tk.Entry(
            duration_frame,
            font=("Arial", 11),
            width=10
        )
        self.duration_entry.pack(side='left', padx=(10, 0))
        self.duration_entry.insert(0, "365")  # افتراضي سنة
        
        # أزرار
        buttons_frame = tk.Frame(new_license_frame, bg='#34495e')
        buttons_frame.pack(pady=15)
        
        generate_btn = tk.Button(
            buttons_frame,
            text="🔑 إصدار كود الترخيص",
            font=("Arial", 12, "bold"),
            bg='#27ae60',
            fg='white',
            padx=20,
            pady=8,
            command=self.generate_license_code
        )
        generate_btn.pack(side='left', padx=5)
        
        clear_btn = tk.Button(
            buttons_frame,
            text="🗑️ مسح البيانات",
            font=("Arial", 12),
            bg='#e74c3c',
            fg='white',
            padx=20,
            pady=8,
            command=self.clear_form
        )
        clear_btn.pack(side='left', padx=5)
        
        # منطقة عرض كود الترخيص
        result_frame = tk.Frame(new_license_frame, bg='#34495e')
        result_frame.pack(fill='x', pady=10)
        
        tk.Label(
            result_frame,
            text="📋 كود الترخيص المُصدر:",
            font=("Arial", 11, "bold"),
            bg='#34495e',
            fg='#ecf0f1'
        ).pack(anchor='w')
        
        self.license_code_text = scrolledtext.ScrolledText(
            result_frame,
            height=4,
            font=("Courier", 10),
            wrap=tk.WORD
        )
        self.license_code_text.pack(fill='x', pady=5)
        
    def create_licenses_list_frame(self):
        """إطار عرض التراخيص المصدرة"""
        licenses_frame = tk.LabelFrame(
            self.root,
            text="📊 التراخيص المصدرة",
            font=("Arial", 14, "bold"),
            bg='#34495e',
            fg='#ecf0f1',
            padx=20,
            pady=15
        )
        licenses_frame.pack(pady=10, padx=20, fill='both', expand=True)
        
        # جدول التراخيص
        columns = ('العميل', 'كود العميل', 'رقم الجهاز', 'تاريخ الإصدار', 'تاريخ الانتهاء', 'الحالة')
        self.licenses_tree = ttk.Treeview(licenses_frame, columns=columns, show='headings', height=8)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.licenses_tree.heading(col, text=col)
            self.licenses_tree.column(col, width=120)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(licenses_frame, orient='vertical', command=self.licenses_tree.yview)
        self.licenses_tree.configure(yscrollcommand=scrollbar.set)
        
        self.licenses_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # تحديث قائمة التراخيص
        self.refresh_licenses_list()
        
    def generate_license_code(self):
        """إصدار كود ترخيص جديد"""
        # التحقق من البيانات
        customer_name = self.customer_name_entry.get().strip()
        customer_code = self.customer_code_entry.get().strip()
        machine_id = self.machine_id_entry.get().strip()
        duration_str = self.duration_entry.get().strip()
        
        if not all([customer_name, customer_code, machine_id, duration_str]):
            messagebox.showerror("خطأ", "يرجى ملء جميع البيانات المطلوبة")
            return
        
        try:
            duration_days = int(duration_str)
            if duration_days <= 0:
                raise ValueError()
        except ValueError:
            messagebox.showerror("خطأ", "مدة الترخيص يجب أن تكون رقم صحيح أكبر من صفر")
            return
        
        # حساب تاريخ الانتهاء
        issue_date = datetime.now()
        expiry_date = issue_date + timedelta(days=duration_days)
        
        # إنشاء كود الترخيص
        license_code = self.create_license_code(customer_code, machine_id, expiry_date)
        
        # حفظ في قاعدة البيانات
        license_id = f"{customer_code}_{machine_id}_{int(issue_date.timestamp())}"
        self.issued_licenses[license_id] = {
            "customer_name": customer_name,
            "customer_code": customer_code,
            "machine_id": machine_id,
            "license_code": license_code,
            "issue_date": issue_date.isoformat(),
            "expiry_date": expiry_date.isoformat(),
            "duration_days": duration_days,
            "status": "active"
        }
        
        self.save_issued_licenses()
        
        # عرض كود الترخيص
        result_text = f"""
✅ تم إصدار كود الترخيص بنجاح!

👤 العميل: {customer_name}
🔑 كود العميل: {customer_code}
💻 رقم الجهاز: {machine_id}
📅 تاريخ الإصدار: {issue_date.strftime('%d/%m/%Y %H:%M')}
📅 تاريخ الانتهاء: {expiry_date.strftime('%d/%m/%Y')}
⏰ مدة الترخيص: {duration_days} يوم

🔐 كود الترخيص:
{license_code}

📋 أرسل هذا الكود للعميل لتفعيل البرنامج
        """
        
        self.license_code_text.delete(1.0, tk.END)
        self.license_code_text.insert(1.0, result_text)
        
        # تحديث قائمة التراخيص
        self.refresh_licenses_list()
        
        messagebox.showinfo("نجح الإصدار", f"تم إصدار ترخيص للعميل: {customer_name}")
        
    def create_license_code(self, customer_code, machine_id, expiry_date):
        """إنشاء كود الترخيص"""
        # تنسيق التاريخ
        date_str = expiry_date.strftime("%Y%m%d")
        
        # إنشاء hash للتحقق
        data_to_hash = f"{customer_code}{machine_id}{date_str}SICOO_LICENSE_2024"
        hash_value = hashlib.sha256(data_to_hash.encode()).hexdigest()[:8].upper()
        
        # تنسيق الكود النهائي
        license_code = f"SICOO-{date_str}-{customer_code[:6]}-{machine_id[:6]}-{hash_value}"
        
        return license_code
        
    def clear_form(self):
        """مسح بيانات النموذج"""
        self.customer_name_entry.delete(0, tk.END)
        self.customer_code_entry.delete(0, tk.END)
        self.machine_id_entry.delete(0, tk.END)
        self.duration_entry.delete(0, tk.END)
        self.duration_entry.insert(0, "365")
        self.license_code_text.delete(1.0, tk.END)
        
    def refresh_licenses_list(self):
        """تحديث قائمة التراخيص"""
        # مسح القائمة الحالية
        for item in self.licenses_tree.get_children():
            self.licenses_tree.delete(item)
        
        # إضافة التراخيص
        for license_id, license_data in self.issued_licenses.items():
            issue_date = datetime.fromisoformat(license_data['issue_date'])
            expiry_date = datetime.fromisoformat(license_data['expiry_date'])
            
            # تحديد الحالة
            now = datetime.now()
            if now > expiry_date:
                status = "منتهي"
            else:
                days_left = (expiry_date - now).days
                status = f"نشط ({days_left} يوم)"
            
            self.licenses_tree.insert('', 'end', values=(
                license_data['customer_name'],
                license_data['customer_code'],
                license_data['machine_id'],
                issue_date.strftime('%d/%m/%Y'),
                expiry_date.strftime('%d/%m/%Y'),
                status
            ))
    
    def run(self):
        """تشغيل البرنامج"""
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    app = LicenseGeneratorApp()
    app.run()

if __name__ == "__main__":
    main()
