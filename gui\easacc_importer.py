"""
أداة استيراد البيانات من برنامج EasAcc
"""

import os
import pandas as pd
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                           QLabel, QFileDialog, QTableWidget, QTableWidgetItem,
                           QTabWidget, QWidget, QProgressBar, QTextEdit,
                           QMessageBox, QCheckBox, QGroupBox, QGridLayout,
                           QLineEdit, QComboBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont
from sqlalchemy.orm import sessionmaker
from database.models import Customer, Supplier, Product
import sqlite3
from datetime import datetime

# محاولة استيراد pyodbc مع معالجة الخطأ
try:
    import pyodbc
    PYODBC_AVAILABLE = True
except ImportError:
    PYODBC_AVAILABLE = False
    print("تحذير: pyodbc غير متاح. سيتم دعم Excel و CSV فقط.")


class EasAccImporterDialog(QDialog):
    """نافذة استيراد البيانات من برنامج EasAcc"""
    
    def __init__(self, engine, parent=None):
        super().__init__(parent)
        self.engine = engine
        self.easacc_data = {}
        self.duplicate_action = "update"  # خيارات: skip, update, add_new
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("استيراد البيانات من برنامج EasAcc")
        self.setGeometry(100, 100, 1200, 800)
        
        layout = QVBoxLayout(self)
        
        # عنوان الصفحة
        title_label = QLabel("🔄 استيراد البيانات من برنامج EasAcc")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                           stop:0 #3498db, stop:1 #2980b9);
                padding: 15px;
                border-radius: 10px;
                margin-bottom: 10px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # قسم اختيار قاعدة البيانات
        db_group = QGroupBox("📁 اختيار قاعدة بيانات EasAcc")
        db_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                border: 2px solid #3498db;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        db_layout = QGridLayout(db_group)
        
        # نوع قاعدة البيانات
        db_layout.addWidget(QLabel("نوع قاعدة البيانات:"), 0, 0)
        self.db_type_combo = QComboBox()
        self.db_type_combo.addItems([
            "Microsoft Access (.mdb/.accdb)",
            "SQL Server", 
            "Firebird (.fdb)",
            "Excel (.xlsx/.xls)",
            "CSV Files"
        ])
        db_layout.addWidget(self.db_type_combo, 0, 1)
        
        # مسار الملف
        db_layout.addWidget(QLabel("مسار قاعدة البيانات:"), 1, 0)
        self.db_path_edit = QLineEdit()
        self.db_path_edit.setPlaceholderText("اختر ملف قاعدة البيانات...")
        db_layout.addWidget(self.db_path_edit, 1, 1)
        
        browse_btn = QPushButton("📂 استعراض")
        browse_btn.clicked.connect(self.browse_database)
        db_layout.addWidget(browse_btn, 1, 2)
        
        # زر الاتصال
        connect_btn = QPushButton("🔗 الاتصال بقاعدة البيانات")
        connect_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                           stop:0 #27ae60, stop:1 #2ecc71);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                           stop:0 #229954, stop:1 #27ae60);
            }
        """)
        connect_btn.clicked.connect(self.connect_to_database)
        db_layout.addWidget(connect_btn, 2, 0, 1, 3)
        
        layout.addWidget(db_group)
        
        # تبويبات البيانات
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #ddd;
                border-radius: 8px;
            }
            QTabBar::tab {
                background: #f8f9fa;
                border: 1px solid #ddd;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
            }
            QTabBar::tab:selected {
                background: #3498db;
                color: white;
            }
        """)
        
        # تبويب العملاء
        self.customers_tab = self.create_data_tab("العملاء", "customers")
        self.tabs.addTab(self.customers_tab, "👥 العملاء")
        
        # تبويب الموردين
        self.suppliers_tab = self.create_data_tab("الموردين", "suppliers")
        self.tabs.addTab(self.suppliers_tab, "🏢 الموردين")
        
        # تبويب المنتجات
        self.products_tab = self.create_data_tab("المنتجات", "products")
        self.tabs.addTab(self.products_tab, "📦 المنتجات")
        
        layout.addWidget(self.tabs)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # منطقة الرسائل
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #2c3e50;
                color: #ecf0f1;
                border: 1px solid #34495e;
                border-radius: 5px;
                font-family: 'Courier New';
                font-size: 12px;
            }
        """)
        layout.addWidget(self.log_text)
        
        # خيارات التعارض
        conflict_group = QGroupBox("⚠️ إدارة التعارضات")
        conflict_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #e74c3c;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
                background: rgba(231, 76, 60, 0.1);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #e74c3c;
            }
        """)
        conflict_layout = QGridLayout(conflict_group)

        # خيارات التعامل مع التعارضات
        conflict_layout.addWidget(QLabel("عند وجود بيانات مكررة:"), 0, 0)
        self.conflict_combo = QComboBox()
        self.conflict_combo.addItems([
            "تجاهل البيانات المكررة (الافتراضي)",
            "استبدال البيانات الموجودة",
            "دمج البيانات (الأرصدة والكميات)",
            "إظهار نافذة تأكيد لكل تعارض",
            "إضافة جميع المنتجات (تعطيل فحص التكرار)"
        ])
        conflict_layout.addWidget(self.conflict_combo, 0, 1)

        # خيار النسخ الاحتياطي
        self.backup_cb = QCheckBox("إنشاء نسخة احتياطية قبل الاستيراد")
        self.backup_cb.setChecked(True)
        self.backup_cb.setStyleSheet("color: #e74c3c; font-weight: bold;")
        conflict_layout.addWidget(self.backup_cb, 1, 0, 1, 2)

        layout.addWidget(conflict_group)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        import_btn = QPushButton("📥 استيراد البيانات المحددة")
        import_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                           stop:0 #e74c3c, stop:1 #c0392b);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                           stop:0 #c0392b, stop:1 #a93226);
            }
        """)
        import_btn.clicked.connect(self.import_selected_data)
        buttons_layout.addWidget(import_btn)

        close_btn = QPushButton("❌ إغلاق")
        close_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                           stop:0 #95a5a6, stop:1 #7f8c8d);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                           stop:0 #7f8c8d, stop:1 #6c7b7d);
            }
        """)
        close_btn.clicked.connect(self.close)
        buttons_layout.addWidget(close_btn)

        layout.addLayout(buttons_layout)

    def get_action_description(self):
        """وصف نوع التعامل مع التعارضات"""
        actions = {
            "skip": "تجاهل البيانات المكررة",
            "update": "تحديث البيانات الموجودة",
            "merge": "دمج البيانات",
            "ask": "السؤال عن كل تعارض",
            "force_add": "إضافة جميع المنتجات (تعطيل فحص التكرار)"
        }
        return actions.get(self.duplicate_action, "غير محدد")

    def check_product_duplicate(self, session, product):
        """
        فحص ما إذا كان المنتج مكرر أم لا
        المنتج يعتبر مكرر إذا كان له:
        1. نفس الاسم الكامل + نفس الباركود
        2. أو نفس الاسم الكامل (للمنتجات بدون باركود)

        Returns:
            tuple: (existing_product, duplicate_type)
        """
        existing_product = None
        duplicate_type = ""

        if not product.name or product.name.strip() == "":
            return None, ""

        product_name = product.name.strip()
        product_barcode = product.barcode.strip() if product.barcode else ""

        # حالة 1: البحث عن منتج بنفس الاسم + نفس الباركود
        if product_name and product_barcode:
            existing_product = session.query(Product).filter(
                Product.name == product_name,
                Product.barcode == product_barcode
            ).first()
            if existing_product:
                duplicate_type = "بالاسم والباركود"
                return existing_product, duplicate_type

        # حالة 2: البحث عن منتج بنفس الاسم (للمنتجات بدون باركود) - مع شروط أكثر صرامة
        if product_name and not product_barcode:
            # البحث عن منتج بنفس الاسم الدقيق وبدون باركود أيضاً
            existing_product = session.query(Product).filter(
                Product.name == product_name,
                (Product.barcode.is_(None) | (Product.barcode == "") | (Product.barcode == ""))
            ).first()

            # إضافة شرط إضافي: يجب أن يكون الاسم مطابق تماماً وليس مجرد متشابه
            if existing_product and existing_product.name.strip().lower() == product_name.strip().lower():
                duplicate_type = "بالاسم (بدون باركود)"
                return existing_product, duplicate_type

        return None, ""

    def update_product_data(self, existing_product, new_product, merge_quantities=False):
        """
        تحديث بيانات المنتج الموجود بالبيانات الجديدة

        Args:
            existing_product: المنتج الموجود في قاعدة البيانات
            new_product: المنتج الجديد من ملف الاستيراد
            merge_quantities: إذا كان True، يجمع الكميات، وإلا يستبدلها
        """
        # تحديث البيانات الأساسية (فقط إذا كانت البيانات الجديدة غير فارغة)
        if new_product.description and new_product.description.strip():
            existing_product.description = new_product.description.strip()

        if new_product.category and new_product.category.strip():
            existing_product.category = new_product.category.strip()

        if hasattr(new_product, 'purchase_price') and new_product.purchase_price > 0:
            existing_product.purchase_price = new_product.purchase_price

        if hasattr(new_product, 'sale_price') and new_product.sale_price > 0:
            existing_product.sale_price = new_product.sale_price

        if new_product.unit and new_product.unit.strip():
            existing_product.unit = new_product.unit.strip()

        if new_product.barcode and new_product.barcode.strip():
            existing_product.barcode = new_product.barcode.strip()

        # التعامل مع الكمية
        if hasattr(new_product, 'quantity') and new_product.quantity is not None:
            if merge_quantities:
                # جمع الكميات
                existing_product.quantity = (existing_product.quantity or 0) + (new_product.quantity or 0)
            else:
                # استبدال الكمية
                existing_product.quantity = new_product.quantity

        # تحديث وقت التعديل
        existing_product.updated_at = datetime.now()

    def refresh_inventory_if_open(self):
        """تحديث واجهة المخزون إذا كانت مفتوحة"""
        try:
            # البحث عن النافذة الرئيسية
            main_window = self.parent()
            if main_window and hasattr(main_window, 'main_tabs'):
                # البحث عن تبويب المخزون
                for i in range(main_window.main_tabs.count()):
                    tab_widget = main_window.main_tabs.widget(i)
                    if hasattr(tab_widget, 'refresh_products'):
                        # وجدنا واجهة المخزون، قم بتحديثها
                        tab_widget.refresh_products()
                        self.log_message("🔄 تم تحديث واجهة المخزون تلقائياً")
                        return

            self.log_message("ℹ️ لم يتم العثور على واجهة المخزون المفتوحة")
        except Exception as e:
            self.log_message(f"⚠️ خطأ في تحديث واجهة المخزون: {e}")
        
    def create_data_tab(self, title, data_type):
        """إنشاء تبويب لعرض البيانات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # خيارات الاستيراد
        options_layout = QHBoxLayout()
        
        select_all_cb = QCheckBox(f"تحديد جميع {title}")
        select_all_cb.setChecked(True)
        select_all_cb.stateChanged.connect(
            lambda state, dt=data_type: self.toggle_all_selection(dt, state)
        )
        options_layout.addWidget(select_all_cb)
        
        options_layout.addStretch()
        
        count_label = QLabel("0 عنصر")
        count_label.setObjectName(f"{data_type}_count")
        options_layout.addWidget(count_label)
        
        layout.addLayout(options_layout)
        
        # جدول البيانات
        table = QTableWidget()
        table.setObjectName(f"{data_type}_table")
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #ddd;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QHeaderView::section {
                background-color: #3498db;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
        layout.addWidget(table)
        
        return widget

    def update_customer_data(self, existing_customer, new_customer):
        """تحديث بيانات العميل الموجود"""
        existing_customer.phone = new_customer.phone or existing_customer.phone
        existing_customer.address = new_customer.address or existing_customer.address
        existing_customer.email = new_customer.email or existing_customer.email
        existing_customer.credit_limit = new_customer.credit_limit or existing_customer.credit_limit
        # لا نحدث الرصيد في وضع التحديث

    def merge_customer_data(self, existing_customer, new_customer):
        """دمج بيانات العميل (جمع الأرصدة)"""
        existing_customer.phone = new_customer.phone or existing_customer.phone
        existing_customer.address = new_customer.address or existing_customer.address
        existing_customer.email = new_customer.email or existing_customer.email
        existing_customer.credit_limit = max(new_customer.credit_limit or 0, existing_customer.credit_limit or 0)
        # جمع الأرصدة
        existing_customer.balance = (existing_customer.balance or 0) + (new_customer.balance or 0)

    def show_conflict_dialog(self, data_type, existing_name, new_data):
        """عرض نافذة تأكيد التعارض"""
        reply = QMessageBox.question(
            self, "تعارض في البيانات",
            f"يوجد {data_type} بنفس الاسم: {existing_name}\n\n"
            f"هل تريد استبدال البيانات الموجودة؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        return reply == QMessageBox.Yes

    def browse_database(self):
        """استعراض ملف قاعدة البيانات"""
        db_type = self.db_type_combo.currentText()
        
        if "Access" in db_type:
            file_filter = "Access Database (*.mdb *.accdb)"
        elif "Excel" in db_type:
            file_filter = "Excel Files (*.xlsx *.xls)"
        elif "Firebird" in db_type:
            file_filter = "Firebird Database (*.fdb)"
        elif "CSV" in db_type:
            file_filter = "CSV Files (*.csv)"
        else:
            file_filter = "All Files (*.*)"
            
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختيار ملف قاعدة البيانات", "", file_filter
        )
        
        if file_path:
            self.db_path_edit.setText(file_path)
            
    def log_message(self, message):
        """إضافة رسالة إلى سجل الأحداث"""
        self.log_text.append(f"[{pd.Timestamp.now().strftime('%H:%M:%S')}] {message}")
        
    def connect_to_database(self):
        """الاتصال بقاعدة البيانات وتحميل البيانات"""
        db_path = self.db_path_edit.text().strip()
        if not db_path:
            QMessageBox.warning(self, "تنبيه", "الرجاء اختيار ملف قاعدة البيانات")
            return
            
        if not os.path.exists(db_path):
            QMessageBox.critical(self, "خطأ", "الملف المحدد غير موجود")
            return
            
        self.log_message("بدء الاتصال بقاعدة البيانات...")
        
        try:
            db_type = self.db_type_combo.currentText()
            
            if "Access" in db_type:
                self.load_from_access(db_path)
            elif "Excel" in db_type:
                self.load_from_excel(db_path)
            elif "CSV" in db_type:
                self.load_from_csv(db_path)
            else:
                QMessageBox.warning(self, "تنبيه", "نوع قاعدة البيانات غير مدعوم حالياً")
                
        except Exception as e:
            self.log_message(f"خطأ في الاتصال: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"فشل الاتصال بقاعدة البيانات:\n{str(e)}")

    def load_from_access(self, db_path):
        """تحميل البيانات من قاعدة بيانات Access"""
        if not PYODBC_AVAILABLE:
            self.log_message("خطأ: مكتبة pyodbc غير متاحة")
            QMessageBox.critical(
                self, "خطأ",
                "مكتبة pyodbc غير مثبتة!\n\n"
                "لاستيراد بيانات Access، يرجى:\n"
                "1. تثبيت pyodbc: pip install pyodbc\n"
                "2. أو تصدير البيانات إلى Excel/CSV من برنامج EasAcc\n\n"
                "يمكنك استخدام Excel أو CSV كبديل."
            )
            return

        try:
            # محاولة الاتصال بقاعدة بيانات Access
            conn_str = f"DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={db_path};"
            conn = pyodbc.connect(conn_str)

            self.log_message("تم الاتصال بقاعدة البيانات بنجاح")

            # تحميل العملاء
            self.load_customers_from_access(conn)

            # تحميل الموردين
            self.load_suppliers_from_access(conn)

            # تحميل المنتجات
            self.load_products_from_access(conn)

            conn.close()
            self.log_message("تم تحميل جميع البيانات بنجاح")

        except Exception as e:
            self.log_message(f"خطأ في تحميل بيانات Access: {str(e)}")
            QMessageBox.critical(
                self, "خطأ",
                f"فشل في الاتصال بقاعدة بيانات Access:\n{str(e)}\n\n"
                "تأكد من:\n"
                "1. تثبيت Microsoft Access Database Engine\n"
                "2. صحة مسار الملف\n"
                "3. أن الملف غير مفتوح في برنامج آخر"
            )

    def load_customers_from_access(self, conn):
        """تحميل العملاء من قاعدة بيانات Access"""
        try:
            # جداول العملاء المحتملة في EasAcc
            possible_tables = ['Customers', 'Customer', 'Clients', 'Client', 'العملاء']

            customers_df = None
            for table_name in possible_tables:
                try:
                    query = f"SELECT * FROM {table_name}"
                    customers_df = pd.read_sql(query, conn)
                    self.log_message(f"تم العثور على جدول العملاء: {table_name}")
                    break
                except:
                    continue

            if customers_df is not None and not customers_df.empty:
                self.easacc_data['customers'] = customers_df
                self.populate_table('customers', customers_df)
                self.log_message(f"تم تحميل {len(customers_df)} عميل")
            else:
                self.log_message("لم يتم العثور على جدول العملاء")

        except Exception as e:
            self.log_message(f"خطأ في تحميل العملاء: {str(e)}")

    def load_suppliers_from_access(self, conn):
        """تحميل الموردين من قاعدة بيانات Access"""
        try:
            # جداول الموردين المحتملة في EasAcc
            possible_tables = ['Suppliers', 'Supplier', 'Vendors', 'Vendor', 'الموردين']

            suppliers_df = None
            for table_name in possible_tables:
                try:
                    query = f"SELECT * FROM {table_name}"
                    suppliers_df = pd.read_sql(query, conn)
                    self.log_message(f"تم العثور على جدول الموردين: {table_name}")
                    break
                except:
                    continue

            if suppliers_df is not None and not suppliers_df.empty:
                self.easacc_data['suppliers'] = suppliers_df
                self.populate_table('suppliers', suppliers_df)
                self.log_message(f"تم تحميل {len(suppliers_df)} مورد")
            else:
                self.log_message("لم يتم العثور على جدول الموردين")

        except Exception as e:
            self.log_message(f"خطأ في تحميل الموردين: {str(e)}")

    def load_products_from_access(self, conn):
        """تحميل المنتجات من قاعدة بيانات Access"""
        try:
            # جداول المنتجات المحتملة في EasAcc
            possible_tables = ['Products', 'Product', 'Items', 'Item', 'الاصناف', 'المنتجات']

            products_df = None
            for table_name in possible_tables:
                try:
                    query = f"SELECT * FROM {table_name}"
                    products_df = pd.read_sql(query, conn)
                    self.log_message(f"تم العثور على جدول المنتجات: {table_name}")
                    break
                except:
                    continue

            if products_df is not None and not products_df.empty:
                self.easacc_data['products'] = products_df
                self.populate_table('products', products_df)
                self.log_message(f"تم تحميل {len(products_df)} منتج")
            else:
                self.log_message("لم يتم العثور على جدول المنتجات")

        except Exception as e:
            self.log_message(f"خطأ في تحميل المنتجات: {str(e)}")

    def load_from_excel(self, file_path):
        """تحميل البيانات من ملف Excel"""
        try:
            # قراءة جميع الأوراق
            excel_file = pd.ExcelFile(file_path)
            self.log_message(f"تم العثور على {len(excel_file.sheet_names)} ورقة في الملف")

            for sheet_name in excel_file.sheet_names:
                df = pd.read_excel(file_path, sheet_name=sheet_name)

                # تنظيف البيانات - إزالة الصفوف الفارغة
                df = df.dropna(how='all')

                if df.empty:
                    self.log_message(f"ورقة {sheet_name} فارغة، تم تخطيها")
                    continue

                # تحديد نوع البيانات بناءً على اسم الورقة أو محتواها
                data_type_detected = False

                # فحص اسم الورقة أولاً
                if any(keyword in sheet_name.lower() for keyword in ['customer', 'client', 'عميل', 'عملاء']):
                    self.easacc_data['customers'] = df
                    self.populate_table('customers', df)
                    self.log_message(f"تم تحميل {len(df)} عميل من ورقة {sheet_name}")
                    data_type_detected = True

                elif any(keyword in sheet_name.lower() for keyword in ['supplier', 'vendor', 'مورد', 'موردين']):
                    self.easacc_data['suppliers'] = df
                    self.populate_table('suppliers', df)
                    self.log_message(f"تم تحميل {len(df)} مورد من ورقة {sheet_name}")
                    data_type_detected = True

                elif any(keyword in sheet_name.lower() for keyword in ['product', 'item', 'منتج', 'صنف', 'اصناف', 'منتجات']):
                    self.easacc_data['products'] = df
                    self.populate_table('products', df)
                    self.log_message(f"تم تحميل {len(df)} منتج من ورقة {sheet_name}")
                    data_type_detected = True

                # إذا لم يتم تحديد النوع من اسم الورقة، فحص محتوى الأعمدة
                if not data_type_detected:
                    detected_type = self.detect_data_type_from_columns(df)
                    if detected_type:
                        self.easacc_data[detected_type] = df
                        self.populate_table(detected_type, df)
                        self.log_message(f"تم تحميل {len(df)} عنصر من نوع {detected_type} من ورقة {sheet_name}")
                        data_type_detected = True

                # إذا لم يتم تحديد النوع، اعرض خيارات للمستخدم
                if not data_type_detected:
                    self.log_message(f"لم يتم تحديد نوع البيانات في ورقة {sheet_name}")
                    selected_type = self.show_data_type_selection(df, sheet_name)
                    if selected_type:
                        self.easacc_data[selected_type] = df
                        self.populate_table(selected_type, df)
                        self.log_message(f"تم تحميل {len(df)} عنصر من نوع {selected_type} من ورقة {sheet_name}")

        except Exception as e:
            self.log_message(f"خطأ في تحميل ملف Excel: {str(e)}")
            raise

    def load_from_csv(self, file_path):
        """تحميل البيانات من ملف CSV"""
        try:
            df = pd.read_csv(file_path, encoding='utf-8-sig')

            # تحديد نوع البيانات بناءً على اسم الملف
            file_name = os.path.basename(file_path).lower()

            if any(keyword in file_name for keyword in ['customer', 'client', 'عميل', 'عملاء']):
                self.easacc_data['customers'] = df
                self.populate_table('customers', df)
                self.log_message(f"تم تحميل {len(df)} عميل من ملف CSV")

            elif any(keyword in file_name for keyword in ['supplier', 'vendor', 'مورد', 'موردين']):
                self.easacc_data['suppliers'] = df
                self.populate_table('suppliers', df)
                self.log_message(f"تم تحميل {len(df)} مورد من ملف CSV")

            elif any(keyword in file_name for keyword in ['product', 'item', 'منتج', 'صنف', 'اصناف']):
                self.easacc_data['products'] = df
                self.populate_table('products', df)
                self.log_message(f"تم تحميل {len(df)} منتج من ملف CSV")
            else:
                # إذا لم يتم تحديد النوع، اعرض خيارات للمستخدم
                self.show_data_type_selection(df)

        except Exception as e:
            self.log_message(f"خطأ في تحميل ملف CSV: {str(e)}")
            raise

    def populate_table(self, data_type, df):
        """ملء الجدول بالبيانات"""
        table = self.findChild(QTableWidget, f"{data_type}_table")
        count_label = self.findChild(QLabel, f"{data_type}_count")

        if table is None or df is None or df.empty:
            return

        # إعداد الجدول
        table.setRowCount(len(df))
        table.setColumnCount(len(df.columns))
        table.setHorizontalHeaderLabels(df.columns.tolist())

        # ملء البيانات
        for row in range(len(df)):
            for col in range(len(df.columns)):
                value = str(df.iloc[row, col]) if pd.notna(df.iloc[row, col]) else ""
                item = QTableWidgetItem(value)
                item.setFlags(item.flags() | Qt.ItemIsUserCheckable)
                item.setCheckState(Qt.Checked)
                table.setItem(row, col, item)

        # تحديث العداد
        count_label.setText(f"{len(df)} عنصر")

        # تعديل حجم الأعمدة
        table.resizeColumnsToContents()

    def toggle_all_selection(self, data_type, state):
        """تحديد/إلغاء تحديد جميع العناصر"""
        table = self.findChild(QTableWidget, f"{data_type}_table")
        if table is None:
            return

        check_state = Qt.Checked if state == Qt.Checked else Qt.Unchecked

        for row in range(table.rowCount()):
            item = table.item(row, 0)
            if item:
                item.setCheckState(check_state)

    def import_selected_data(self):
        """استيراد البيانات المحددة مع إدارة التعارضات"""
        session = None
        try:
            # تحديد نوع التعامل مع التعارضات
            conflict_option = self.conflict_combo.currentIndex()
            if conflict_option == 0:
                self.duplicate_action = "skip"
            elif conflict_option == 1:
                self.duplicate_action = "update"
            elif conflict_option == 2:
                self.duplicate_action = "merge"
            elif conflict_option == 3:
                self.duplicate_action = "ask"
            else:  # conflict_option == 4
                self.duplicate_action = "force_add"

            self.log_message(f"🔧 تم تعيين سلوك التعارضات: {self.get_action_description()}")

            # إنشاء نسخة احتياطية إذا كان مطلوب
            if self.backup_cb.isChecked():
                if not self.create_backup():
                    return

            # إنشاء جلسة منفصلة
            Session = sessionmaker(bind=self.engine)
            session = Session()

            try:
                # استيراد العملاء
                if 'customers' in self.easacc_data:
                    self.log_message("بدء استيراد العملاء...")
                    self.import_customers(session)
                    session.flush()  # تنفيذ التغييرات بدون commit

                # استيراد الموردين
                if 'suppliers' in self.easacc_data:
                    self.log_message("بدء استيراد الموردين...")
                    self.import_suppliers(session)
                    session.flush()  # تنفيذ التغييرات بدون commit

                # استيراد المنتجات
                if 'products' in self.easacc_data:
                    self.log_message("بدء استيراد المنتجات...")
                    self.import_products(session)
                    session.flush()  # تنفيذ التغييرات بدون commit

                # حفظ نهائي
                self.log_message("💾 بدء حفظ البيانات في قاعدة البيانات...")
                session.commit()
                self.log_message("✅ تم حفظ جميع البيانات في قاعدة البيانات بنجاح!")

                # التحقق من العدد الجديد
                try:
                    new_count = session.query(Product).count()
                    self.log_message(f"📊 العدد الجديد للمنتجات في قاعدة البيانات: {new_count}")
                except Exception as count_error:
                    self.log_message(f"⚠️ خطأ في قراءة العدد الجديد: {count_error}")

                self.log_message("✅ تم استيراد جميع البيانات بنجاح!")

                # محاولة تحديث واجهة المخزون إذا كانت مفتوحة
                self.refresh_inventory_if_open()

                QMessageBox.information(self, "نجح", "تم استيراد البيانات بنجاح!\n\nتم تحديث واجهة المخزون تلقائياً.")

            except Exception as e:
                self.log_message(f"❌ خطأ أثناء الاستيراد، سيتم إلغاء جميع التغييرات: {str(e)}")
                session.rollback()
                self.log_message("🔄 تم إلغاء جميع التغييرات (rollback)")
                raise e

        except Exception as e:
            error_msg = str(e)
            print(f"خطأ في الاستيراد: {error_msg}")

            # رسالة خطأ مبسطة للمستخدم
            if "UNIQUE constraint failed" in error_msg:
                self.log_message("❌ خطأ: يوجد بيانات مكررة")
                QMessageBox.critical(self, "خطأ", "فشل في الاستيراد بسبب وجود بيانات مكررة")
            elif "Session" in error_msg and "rollback" in error_msg:
                self.log_message("❌ خطأ في جلسة قاعدة البيانات")
                QMessageBox.critical(self, "خطأ", "خطأ في جلسة قاعدة البيانات. يرجى المحاولة مرة أخرى")
            else:
                self.log_message(f"❌ خطأ في الاستيراد: {error_msg}")
                QMessageBox.critical(self, "خطأ", f"فشل في استيراد البيانات:\n{error_msg}")

        finally:
            if session:
                session.close()

    def create_backup(self):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            import shutil
            from datetime import datetime

            # مسار قاعدة البيانات الحالية
            db_path = "accounting.db"

            # إنشاء اسم النسخة الاحتياطية
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"backup_before_import_{timestamp}.db"

            # نسخ قاعدة البيانات
            shutil.copy2(db_path, backup_path)

            self.log_message(f"تم إنشاء نسخة احتياطية: {backup_path}")
            return True

        except Exception as e:
            self.log_message(f"فشل في إنشاء النسخة الاحتياطية: {str(e)}")
            reply = QMessageBox.question(
                self, "تحذير",
                f"فشل في إنشاء النسخة الاحتياطية:\n{str(e)}\n\n"
                "هل تريد المتابعة بدون نسخة احتياطية؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            return reply == QMessageBox.Yes

    def import_customers(self, session):
        """استيراد العملاء مع إدارة التعارضات"""
        table = self.findChild(QTableWidget, "customers_table")
        df = self.easacc_data['customers']
        conflict_mode = self.conflict_combo.currentIndex()

        imported_count = 0
        updated_count = 0
        skipped_count = 0

        for row in range(table.rowCount()):
            item = table.item(row, 0)
            if item and item.checkState() == Qt.Checked:
                try:
                    # استخراج بيانات العميل
                    customer_data = {}
                    for col in range(table.columnCount()):
                        header = table.horizontalHeaderItem(col).text()
                        value = table.item(row, col).text()
                        customer_data[header] = value

                    # تحويل البيانات إلى نموذج العميل
                    customer = self.convert_to_customer(customer_data)

                    # التحقق من وجود العميل مسبقاً
                    existing = session.query(Customer).filter(
                        Customer.name == customer.name
                    ).first()

                    if existing:
                        # معالجة التعارض
                        if conflict_mode == 0:  # تجاهل
                            skipped_count += 1
                            self.log_message(f"تم تجاهل العميل المكرر: {customer.name}")
                        elif conflict_mode == 1:  # استبدال
                            self.update_customer_data(existing, customer)
                            updated_count += 1
                            self.log_message(f"تم تحديث العميل: {customer.name}")
                        elif conflict_mode == 2:  # دمج
                            self.merge_customer_data(existing, customer)
                            updated_count += 1
                            self.log_message(f"تم دمج بيانات العميل: {customer.name}")
                        elif conflict_mode == 3:  # تأكيد
                            if self.show_conflict_dialog("عميل", existing.name, customer):
                                self.update_customer_data(existing, customer)
                                updated_count += 1
                            else:
                                skipped_count += 1
                    else:
                        session.add(customer)
                        imported_count += 1

                except Exception as e:
                    self.log_message(f"خطأ في استيراد العميل في الصف {row + 1}: {str(e)}")

        self.log_message(f"تم استيراد {imported_count} عميل جديد، تحديث {updated_count}، تجاهل {skipped_count}")

    def import_suppliers(self, session):
        """استيراد الموردين"""
        table = self.findChild(QTableWidget, "suppliers_table")
        df = self.easacc_data['suppliers']

        imported_count = 0

        for row in range(table.rowCount()):
            item = table.item(row, 0)
            if item and item.checkState() == Qt.Checked:
                try:
                    # استخراج بيانات المورد
                    supplier_data = {}
                    for col in range(table.columnCount()):
                        header = table.horizontalHeaderItem(col).text()
                        value = table.item(row, col).text()
                        supplier_data[header] = value

                    # تحويل البيانات إلى نموذج المورد
                    supplier = self.convert_to_supplier(supplier_data)

                    # التحقق من عدم وجود المورد مسبقاً
                    existing = session.query(Supplier).filter(
                        Supplier.name == supplier.name
                    ).first()

                    if not existing:
                        session.add(supplier)
                        imported_count += 1

                except Exception as e:
                    self.log_message(f"خطأ في استيراد المورد في الصف {row + 1}: {str(e)}")

        self.log_message(f"تم استيراد {imported_count} مورد")

    def import_products(self, session):
        """استيراد المنتجات (محسن)"""
        table = self.findChild(QTableWidget, "products_table")

        if 'products' not in self.easacc_data:
            self.log_message("❌ لا توجد بيانات منتجات للاستيراد")
            return

        # إحصائيات قبل الاستيراد
        products_before = 0
        try:
            products_before = session.query(Product).count()
            self.log_message(f"📊 عدد المنتجات قبل الاستيراد: {products_before}")
        except Exception as e:
            self.log_message(f"⚠️ خطأ في قراءة عدد المنتجات قبل الاستيراد: {e}")

        # عدد المنتجات في ملف الاستيراد
        df = self.easacc_data['products']
        total_in_file = len(df)
        self.log_message(f"📋 عدد المنتجات في ملف الاستيراد: {total_in_file}")

        # رسالة توضيحية عن الاستيراد المحسن
        conflict_option = self.conflict_combo.currentIndex()
        if conflict_option == 4:  # إضافة جميع المنتجات
            self.log_message("📋 وضع الاستيراد: إضافة جميع المنتجات (تعطيل فحص التكرار)")
            self.log_message("   • سيتم إضافة جميع المنتجات كمنتجات جديدة")
            self.log_message("   • لن يتم فحص التكرار")
            self.log_message("   • الأكواد المكررة ستحصل على أرقام تسلسلية")
        elif conflict_option == 0:  # تجاهل المكررة
            self.log_message("📋 وضع الاستيراد: تجاهل المنتجات المكررة")
            self.log_message("   • سيتم فحص التكرار بناءً على الاسم والكود معاً")
            self.log_message("   • المنتجات المكررة سيتم تجاهلها")
        else:
            self.log_message("📋 وضع الاستيراد: معالجة ذكية للتكرار")
            self.log_message("   • سيتم فحص التكرار بناءً على الاسم والكود معاً")
            self.log_message("   • المنتجات المكررة سيتم تحديثها أو دمجها")

        self.log_message("   • سيتم تنظيف الأكواد الفارغة والمكررة تلقائياً")
        self.log_message("   • سيتم تجاهل المنتجات ذات الأسماء الفارغة")

        # إحصائيات قاعدة البيانات قبل الاستيراد
        try:
            existing_products_count = session.query(Product).count()
            self.log_message(f"📊 عدد المنتجات الموجودة قبل الاستيراد: {existing_products_count}")
        except Exception as e:
            self.log_message(f"⚠️ خطأ في قراءة عدد المنتجات: {e}")

        self.log_message("─" * 60)

        if not table:
            self.log_message("❌ لم يتم العثور على جدول المنتجات")
            return

        df = self.easacc_data['products']
        self.log_message(f"🔄 بدء استيراد {len(df)} منتج...")

        imported_count = 0
        skipped_count = 0
        error_count = 0

        # إنشاء مجموعة لتتبع الأكواد المستخدمة في هذا الاستيراد
        used_codes_in_import = set()

        for row in range(table.rowCount()):
            # استيراد جميع المنتجات بغض النظر عن التحديد
            item = table.item(row, 0)
            if item:  # فقط تأكد من وجود البيانات
                try:
                    # استخراج بيانات المنتج
                    product_data = {}
                    for col in range(table.columnCount()):
                        header_item = table.horizontalHeaderItem(col)
                        if header_item:
                            header = header_item.text()
                            cell_item = table.item(row, col)
                            value = cell_item.text() if cell_item else ""
                            product_data[header] = value

                    # تحويل البيانات إلى نموذج المنتج
                    product = self.convert_to_product(product_data)

                    # التحقق من صحة البيانات الأساسية
                    if not product.name or product.name.strip() == "":
                        self.log_message(f"⚠️ تم تخطي الصف {row + 1}: اسم المنتج فارغ")
                        skipped_count += 1
                        continue

                    # تنظيف اسم المنتج
                    product.name = product.name.strip()

                    # تنظيف وإصلاح الكود
                    if product.code:
                        product.code = product.code.strip()
                        # إذا كان الكود فارغاً بعد التنظيف، اجعله None
                        if product.code == "":
                            product.code = None

                    # معالجة الأكواد المكررة في نفس الملف
                    if product.code:
                        original_code = product.code
                        counter = 1
                        while product.code in used_codes_in_import:
                            product.code = f"{original_code}_DUP_{counter}"
                            counter += 1
                        used_codes_in_import.add(product.code)

                    # فحص التكرار في قاعدة البيانات
                    existing_product = None
                    duplicate_reason = ""

                    # تحديد نوع التعامل مع التعارضات
                    conflict_option = self.conflict_combo.currentIndex()

                    if conflict_option == 4:  # إضافة جميع المنتجات - تعطيل فحص التكرار
                        # لا نفحص التكرار، نضيف المنتج مباشرة
                        existing_product = None
                    else:
                        # فحص التكرار الدقيق: نفس الاسم + نفس الكود
                        if product.code:
                            # البحث عن منتج بنفس الاسم ونفس الكود
                            existing_product = session.query(Product).filter(
                                Product.name == product.name,
                                Product.code == product.code
                            ).first()
                            if existing_product:
                                duplicate_reason = f"اسم وكود مطابقين: {product.name} - {product.code}"
                        else:
                            # إذا لم يكن هناك كود، فحص بالاسم فقط ولكن بشروط صارمة
                            existing_product = session.query(Product).filter(
                                Product.name == product.name,
                                (Product.code.is_(None) | (Product.code == ""))
                            ).first()
                            if existing_product:
                                duplicate_reason = f"اسم مطابق (بدون كود): {product.name}"

                    # معالجة المنتج
                    if existing_product:
                        # منتج مكرر - تحديث البيانات
                        self.update_product_data(existing_product, product, merge_quantities=False)
                        skipped_count += 1
                        if row < 10:  # تسجيل أول 10 منتجات فقط
                            self.log_message(f"🔄 تم تحديث المنتج الموجود: {product.name} ({duplicate_reason})")
                    else:
                        # منتج جديد - إضافة
                        try:
                            # تسجيل مفصل للمنتجات
                            if row < 10:  # تسجيل أول 10 منتجات فقط لتجنب الإزعاج
                                self.log_message(f"📦 إضافة المنتج {row + 1}: '{product.name}' | كود: '{product.code}' | باركود: '{product.barcode}'")

                            # التأكد من القيم الافتراضية
                            if not hasattr(product, 'is_active') or product.is_active is None:
                                product.is_active = True

                            # إضافة المنتج
                            session.add(product)
                            session.flush()  # تنفيذ فوري للتحقق من الأخطاء
                            imported_count += 1

                            if row < 10:  # تسجيل أول 10 منتجات فقط
                                self.log_message(f"✅ تم إضافة المنتج بنجاح: {product.name}")

                        except Exception as db_error:
                            session.rollback()  # إلغاء التغييرات المعلقة
                            error_count += 1
                            error_msg = str(db_error)
                            if "UNIQUE constraint failed" in error_msg:
                                if "products.code" in error_msg:
                                    self.log_message(f"❌ خطأ في الصف {row + 1}: الكود '{product.code}' مكرر في قاعدة البيانات")
                                elif "products.name" in error_msg:
                                    self.log_message(f"❌ خطأ في الصف {row + 1}: اسم المنتج '{product.name}' مكرر في قاعدة البيانات")
                                else:
                                    self.log_message(f"❌ خطأ في الصف {row + 1}: بيانات مكررة")
                            else:
                                self.log_message(f"❌ خطأ في قاعدة البيانات للصف {row + 1}: {error_msg}")

                except Exception as e:
                    error_count += 1
                    self.log_message(f"❌ خطأ في استيراد المنتج في الصف {row + 1}: {str(e)}")

        # ملخص النتائج
        total_processed = imported_count + skipped_count + error_count
        self.log_message("─" * 60)
        self.log_message("📊 ملخص استيراد المنتجات:")
        self.log_message(f"✅ تم الاستيراد بنجاح: {imported_count}")
        self.log_message(f"🔄 تم التحديث (مكرر): {skipped_count}")
        self.log_message(f"❌ أخطاء: {error_count}")
        self.log_message(f"📝 إجمالي المعالج: {total_processed}")
        self.log_message(f"🎯 معدل النجاح: {((imported_count + skipped_count)/total_processed*100):.1f}%" if total_processed > 0 else "🎯 معدل النجاح: 0%")

        # إحصائيات بعد الاستيراد
        try:
            products_after = session.query(Product).count()
            self.log_message(f"📊 عدد المنتجات بعد الاستيراد: {products_after}")
            difference = products_after - products_before
            self.log_message(f"📈 الفرق: +{difference} منتج جديد")
        except Exception as e:
            self.log_message(f"⚠️ خطأ في قراءة عدد المنتجات بعد الاستيراد: {e}")

        self.log_message("─" * 60)

    def convert_to_customer(self, data):
        """تحويل بيانات العميل من EasAcc إلى نموذج العميل الجديد"""
        # خريطة الحقول المحتملة في EasAcc
        field_mapping = {
            'name': ['Name', 'CustomerName', 'Client_Name', 'اسم_العميل', 'الاسم'],
            'phone': ['Phone', 'Tel', 'Mobile', 'هاتف', 'تليفون', 'جوال'],
            'address': ['Address', 'عنوان', 'العنوان'],
            'email': ['Email', 'E_Mail', 'بريد_الكتروني'],
            'balance': ['Balance', 'رصيد', 'الرصيد', 'Current_Balance'],
            'credit_limit': ['Credit_Limit', 'حد_الائتمان', 'الحد_الائتماني']
        }

        customer = Customer()

        # تعيين القيم
        for field, possible_keys in field_mapping.items():
            value = self.find_value_by_keys(data, possible_keys)
            if value:
                if field in ['balance', 'credit_limit']:
                    try:
                        setattr(customer, field, float(value.replace(',', '') if isinstance(value, str) else value))
                    except (ValueError, AttributeError):
                        setattr(customer, field, 0.0)
                else:
                    setattr(customer, field, str(value))

        # قيم افتراضية
        if not customer.name:
            customer.name = "عميل غير محدد"
        if not hasattr(customer, 'balance') or customer.balance is None:
            customer.balance = 0.0
        if not hasattr(customer, 'credit_limit') or customer.credit_limit is None:
            customer.credit_limit = 0.0

        return customer

    def convert_to_supplier(self, data):
        """تحويل بيانات المورد من EasAcc إلى نموذج المورد الجديد"""
        # خريطة الحقول المحتملة في EasAcc
        field_mapping = {
            'name': ['Name', 'SupplierName', 'Vendor_Name', 'اسم_المورد', 'الاسم'],
            'phone': ['Phone', 'Tel', 'Mobile', 'هاتف', 'تليفون', 'جوال'],
            'address': ['Address', 'عنوان', 'العنوان'],
            'email': ['Email', 'E_Mail', 'بريد_الكتروني'],
            'balance': ['Balance', 'رصيد', 'الرصيد', 'Current_Balance']
        }

        supplier = Supplier()

        # تعيين القيم
        for field, possible_keys in field_mapping.items():
            value = self.find_value_by_keys(data, possible_keys)
            if value:
                if field == 'balance':
                    try:
                        setattr(supplier, field, float(value.replace(',', '') if isinstance(value, str) else value))
                    except (ValueError, AttributeError):
                        setattr(supplier, field, 0.0)
                else:
                    setattr(supplier, field, str(value))

        # قيم افتراضية
        if not supplier.name:
            supplier.name = "مورد غير محدد"
        if not hasattr(supplier, 'balance') or supplier.balance is None:
            supplier.balance = 0.0

        return supplier

    def convert_to_product(self, data):
        """تحويل بيانات المنتج من EasAcc إلى نموذج المنتج الجديد"""
        # خريطة الحقول المحتملة في EasAcc (محسنة)
        field_mapping = {
            'name': ['Name', 'ProductName', 'Item_Name', 'اسم_المنتج', 'الاسم', 'اسم_الصنف',
                    'اسم المنتج', 'اسم الصنف', 'المنتج', 'الصنف', 'Item Name', 'Product Name'],
            'code': ['Code', 'ProductCode', 'Item_Code', 'كود', 'الكود', 'كود_الصنف',
                    'كود المنتج', 'كود الصنف', 'Product Code', 'Item Code', 'SKU'],
            'description': ['Description', 'وصف', 'الوصف', 'تفاصيل', 'الملاحظات', 'Notes'],
            'category': ['Category', 'فئة', 'الفئة', 'مجموعة', 'المجموعة', 'Group', 'Type'],
            'purchase_price': ['Purchase_Price', 'Cost', 'سعر_الشراء', 'التكلفة', 'سعر الشراء',
                              'تكلفة', 'Purchase Price', 'Cost Price'],
            'sale_price': ['Sale_Price', 'Price', 'سعر_البيع', 'السعر', 'سعر البيع',
                          'Sale Price', 'Selling Price', 'Unit Price'],
            'quantity': ['Quantity', 'Stock', 'كمية', 'الكمية', 'المخزون', 'الرصيد',
                        'Stock Quantity', 'Available', 'Balance'],
            'unit': ['Unit', 'وحدة', 'الوحدة', 'وحدة القياس', 'Unit of Measure', 'UOM'],
            'barcode': ['Barcode', 'باركود', 'الباركود', 'Bar Code', 'EAN', 'UPC'],
            'min_quantity': ['Min_Quantity', 'الحد_الأدنى', 'الحد الأدنى', 'حد أدنى', 'Minimum', 'Min Stock']
        }

        product = Product()

        # تعيين القيم مع تنظيف البيانات
        for field, possible_keys in field_mapping.items():
            value = self.find_value_by_keys(data, possible_keys)
            if value and str(value).strip() and str(value).lower() not in ['nan', 'null', 'none', '']:
                if field in ['purchase_price', 'sale_price']:
                    try:
                        # تنظيف القيمة من الرموز والفواصل
                        clean_value = str(value).replace(',', '').replace('$', '').replace('ج.م', '').replace('ريال', '').strip()
                        setattr(product, field, float(clean_value) if clean_value else 0.0)
                    except (ValueError, AttributeError):
                        setattr(product, field, 0.0)
                elif field in ['quantity', 'min_quantity']:
                    try:
                        clean_value = str(value).replace(',', '').strip()
                        setattr(product, field, int(float(clean_value)) if clean_value else 0)
                    except (ValueError, AttributeError):
                        setattr(product, field, 0)
                else:
                    # تنظيف النصوص
                    clean_text = str(value).strip()
                    setattr(product, field, clean_text if clean_text else None)

        # قيم افتراضية
        if not product.name:
            product.name = "منتج غير محدد"
        # لا ننشئ كود تلقائي - نتركه فارغ إذا لم يكن موجود
        if hasattr(product, 'code') and product.code:
            product.code = product.code.strip()
            if product.code == "":
                product.code = None
        else:
            product.code = None
        if not hasattr(product, 'purchase_price') or product.purchase_price is None:
            product.purchase_price = 0.0
        if not hasattr(product, 'sale_price') or product.sale_price is None:
            product.sale_price = 0.0
        if not hasattr(product, 'quantity') or product.quantity is None:
            product.quantity = 0
        if not hasattr(product, 'min_quantity') or product.min_quantity is None:
            product.min_quantity = 0
        if not product.unit:
            product.unit = "قطعة"

        # التأكد من القيم المطلوبة
        product.is_active = True

        # التحقق من صحة الأسعار
        if product.sale_price <= 0:
            product.sale_price = max(product.purchase_price * 1.2, 1.0)  # هامش ربح 20% كحد أدنى

        return product

    def find_value_by_keys(self, data, possible_keys):
        """البحث عن قيمة باستخدام مفاتيح محتملة (محسن)"""
        # البحث المباشر أولاً
        for key in possible_keys:
            if key in data and data[key] and str(data[key]).strip() and str(data[key]).lower() not in ['nan', 'null', 'none']:
                return data[key]

        # البحث غير الحساس لحالة الأحرف
        data_keys_lower = {k.lower(): v for k, v in data.items()}
        for key in possible_keys:
            key_lower = key.lower()
            if key_lower in data_keys_lower and data_keys_lower[key_lower] and str(data_keys_lower[key_lower]).strip():
                if str(data_keys_lower[key_lower]).lower() not in ['nan', 'null', 'none']:
                    return data_keys_lower[key_lower]

        # البحث الجزئي (يحتوي على الكلمة)
        for key in possible_keys:
            for data_key, value in data.items():
                if (key.lower() in data_key.lower() or data_key.lower() in key.lower()) and value:
                    if str(value).strip() and str(value).lower() not in ['nan', 'null', 'none']:
                        return value

        return None

    def detect_data_type_from_columns(self, df):
        """تحديد نوع البيانات من أسماء الأعمدة"""
        columns = [col.lower() for col in df.columns]

        # كلمات مفتاحية للمنتجات
        product_keywords = ['name', 'productname', 'item_name', 'اسم_المنتج', 'الاسم', 'اسم_الصنف',
                           'code', 'productcode', 'item_code', 'كود', 'الكود', 'كود_الصنف',
                           'price', 'sale_price', 'purchase_price', 'سعر', 'سعر_البيع', 'سعر_الشراء',
                           'quantity', 'stock', 'كمية', 'الكمية', 'المخزون', 'barcode', 'باركود']

        # كلمات مفتاحية للعملاء
        customer_keywords = ['customer', 'client', 'عميل', 'اسم_العميل', 'customer_name',
                           'phone', 'mobile', 'هاتف', 'جوال', 'address', 'عنوان']

        # كلمات مفتاحية للموردين
        supplier_keywords = ['supplier', 'vendor', 'مورد', 'اسم_المورد', 'supplier_name',
                           'company', 'شركة', 'contact', 'اتصال']

        # حساب التطابقات
        product_matches = sum(1 for col in columns if any(keyword in col for keyword in product_keywords))
        customer_matches = sum(1 for col in columns if any(keyword in col for keyword in customer_keywords))
        supplier_matches = sum(1 for col in columns if any(keyword in col for keyword in supplier_keywords))

        # تحديد النوع بناءً على أعلى تطابق
        if product_matches >= customer_matches and product_matches >= supplier_matches and product_matches > 0:
            return 'products'
        elif customer_matches >= supplier_matches and customer_matches > 0:
            return 'customers'
        elif supplier_matches > 0:
            return 'suppliers'

        return None

    def show_data_type_selection(self, df, sheet_name=""):
        """عرض نافذة لاختيار نوع البيانات"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel, QComboBox, QPushButton, QHBoxLayout, QTextEdit

        dialog = QDialog(self)
        dialog.setWindowTitle("تحديد نوع البيانات")
        dialog.setModal(True)
        dialog.resize(500, 400)

        layout = QVBoxLayout(dialog)

        # عرض معلومات الورقة
        info_label = QLabel(f"ورقة: {sheet_name}\nعدد الصفوف: {len(df)}\nعدد الأعمدة: {len(df.columns)}")
        info_label.setStyleSheet("font-weight: bold; padding: 10px; background-color: #f0f0f0; border-radius: 5px;")
        layout.addWidget(info_label)

        layout.addWidget(QLabel("الرجاء تحديد نوع البيانات في هذا الملف:"))

        # عرض عينة من البيانات
        sample_label = QLabel("عينة من البيانات:")
        sample_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
        layout.addWidget(sample_label)

        sample_text = QTextEdit()
        sample_text.setMaximumHeight(150)
        sample_text.setReadOnly(True)

        # عرض أول 3 صفوف كعينة
        sample_data = "الأعمدة: " + ", ".join(df.columns.tolist()) + "\n\n"
        for i in range(min(3, len(df))):
            row_data = []
            for col in df.columns:
                value = str(df.iloc[i][col]) if pd.notna(df.iloc[i][col]) else ""
                row_data.append(f"{col}: {value}")
            sample_data += f"الصف {i+1}: " + " | ".join(row_data) + "\n"

        sample_text.setPlainText(sample_data)
        layout.addWidget(sample_text)

        combo = QComboBox()
        combo.addItems(["المنتجات", "العملاء", "الموردين", "تخطي هذه الورقة"])
        layout.addWidget(combo)

        buttons_layout = QHBoxLayout()
        ok_btn = QPushButton("موافق")
        cancel_btn = QPushButton("إلغاء")

        ok_btn.clicked.connect(dialog.accept)
        cancel_btn.clicked.connect(dialog.reject)

        buttons_layout.addWidget(ok_btn)
        buttons_layout.addWidget(cancel_btn)
        layout.addLayout(buttons_layout)

        if dialog.exec_() == QDialog.Accepted:
            data_type = combo.currentText()
            if data_type == "العملاء":
                return 'customers'
            elif data_type == "الموردين":
                return 'suppliers'
            elif data_type == "المنتجات":
                return 'products'
            else:  # تخطي هذه الورقة
                return None

        return None
