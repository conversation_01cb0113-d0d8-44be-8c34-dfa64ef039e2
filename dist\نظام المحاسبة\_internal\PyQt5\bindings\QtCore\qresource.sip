// qresource.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QResource
{
%TypeHeaderCode
#include <qresource.h>
%End

public:
    QResource(const QString &fileName = QString(), const QLocale &locale = QLocale());
    ~QResource();
    QString absoluteFilePath() const;
    SIP_PYOBJECT data() const /TypeHint="Py_v3:bytes;str"/;
%MethodCode
        // The data may contain embedded '\0's so set the size explicitly.
        
        if (sipCpp->data())
        {
            if ((sipRes = SIPBytes_FromStringAndSize((char *)sipCpp->data(), sipCpp->size())) == NULL)
                sipIsErr = 1;
        }
        else
        {
            Py_INCREF(Py_None);
            sipRes = Py_None;
        }
%End

    QString fileName() const;
    bool isCompressed() const;
    bool isValid() const;
    QLocale locale() const;
    void setFileName(const QString &file);
    void setLocale(const QLocale &locale);
    qint64 size() const;
    static bool registerResource(const QString &rccFileName, const QString &mapRoot = QString());
    static bool registerResource(const uchar *rccData, const QString &mapRoot = QString()) /PyName=registerResourceData/;
    static bool unregisterResource(const QString &rccFileName, const QString &mapRoot = QString());
    static bool unregisterResource(const uchar *rccData, const QString &mapRoot = QString()) /PyName=unregisterResourceData/;

protected:
    QStringList children() const;
    bool isDir() const;
    bool isFile() const;

public:
%If (Qt_5_8_0 -)
    QDateTime lastModified() const;
%End
%If (Qt_5_13_0 -)

    enum Compression
    {
        NoCompression,
        ZlibCompression,
        ZstdCompression,
    };

%End
%If (Qt_5_13_0 -)
    QResource::Compression compressionAlgorithm() const;
%End
%If (Qt_5_15_0 -)
    qint64 uncompressedSize() const;
%End
%If (Qt_5_15_0 -)
    QByteArray uncompressedData() const;
%End

private:
    QResource(const QResource &);
};
