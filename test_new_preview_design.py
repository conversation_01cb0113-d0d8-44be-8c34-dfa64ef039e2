#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار التصميم الجديد لمعاينة الطباعة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMessageBox
from sqlalchemy.orm import Session
from database.models import Transaction, TransactionType
from utils.advanced_invoice_printer import show_advanced_print_dialog

def get_engine():
    """الحصول على محرك قاعدة البيانات"""
    from sqlalchemy import create_engine
    
    # مسار قاعدة البيانات
    db_path = "accounting.db"
    if not os.path.exists(db_path):
        print(f"❌ لم يتم العثور على قاعدة البيانات: {db_path}")
        return None
    
    engine = create_engine(f'sqlite:///{db_path}', echo=False)
    return engine

def main():
    print("🎨 اختبار التصميم الجديد لمعاينة الطباعة...")
    print("=" * 60)
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    
    # الاتصال بقاعدة البيانات
    engine = get_engine()
    if not engine:
        print("❌ فشل في الاتصال بقاعدة البيانات")
        return
    
    try:
        print("🔍 البحث عن آخر فاتورة...")
        
        # البحث عن آخر فاتورة مبيعات
        with Session(engine) as session:
            last_invoice = session.query(Transaction).filter(
                Transaction.type == TransactionType.SALE
            ).order_by(Transaction.id.desc()).first()
            
            if last_invoice:
                print(f"✅ تم العثور على الفاتورة رقم {last_invoice.id}")
                print("🖨️ فتح نافذة الطباعة مع التصميم الجديد...")
                
                # فتح نافذة الطباعة
                show_advanced_print_dialog(engine, last_invoice.id, None)
                
                print("✅ تم فتح نافذة الطباعة!")
                print("")
                print("🎯 التحسينات الجديدة:")
                print("   ✅ تصميم جديد للمعاينة فقط")
                print("   ✅ التصميم القديم للطباعة والحفظ")
                print("   ✅ ترتيب الأعمدة: الإجمالي | الوحدة | السعر | الكمية | المنتج")
                print("   ✅ تصميم مبسط وواضح")
                print("")
                print("📋 كيفية الاختبار:")
                print("   👁️ المعاينة: ستظهر التصميم الجديد")
                print("   🖨️ الطباعة: ستستخدم التصميم القديم")
                print("   💾 حفظ PDF: سيستخدم التصميم القديم")
                
                app.exec_()
                
            else:
                print("❌ لم يتم العثور على أي فاتورة مبيعات")
                print("💡 تأكد من وجود فواتير في قاعدة البيانات")
                
    except Exception as e:
        print(f"❌ خطأ في اختبار التصميم: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
