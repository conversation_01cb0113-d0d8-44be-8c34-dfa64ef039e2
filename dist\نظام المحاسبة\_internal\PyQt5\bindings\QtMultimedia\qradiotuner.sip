// qradiotuner.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QRadioTuner : public QMediaObject
{
%TypeHeaderCode
#include <qradiotuner.h>
%End

public:
    enum State
    {
        ActiveState,
        StoppedState,
    };

    enum Band
    {
        AM,
        FM,
        SW,
        LW,
        FM2,
    };

    enum Error
    {
        NoError,
        ResourceError,
        OpenError,
        OutOfRangeError,
    };

    enum StereoMode
    {
        ForceStereo,
        ForceMono,
        Auto,
    };

    enum SearchMode
    {
        SearchFast,
        SearchGetStationId,
    };

%If (Qt_5_6_1 -)
    explicit QRadioTuner(QObject *parent /TransferThis/ = 0);
%End
%If (- Qt_5_6_1)
    QRadioTuner(QObject *parent /TransferThis/ = 0);
%End
    virtual ~QRadioTuner();
    virtual QMultimedia::AvailabilityStatus availability() const;
    QRadioTuner::State state() const;
    QRadioTuner::Band band() const;
    bool isBandSupported(QRadioTuner::Band b) const;
    int frequency() const;
    int frequencyStep(QRadioTuner::Band band) const;
    QPair<int, int> frequencyRange(QRadioTuner::Band band) const;
    bool isStereo() const;
    void setStereoMode(QRadioTuner::StereoMode mode);
    QRadioTuner::StereoMode stereoMode() const;
    int signalStrength() const;
    int volume() const;
    bool isMuted() const;
    bool isSearching() const;
    bool isAntennaConnected() const;
    QRadioTuner::Error error() const;
    QString errorString() const;
    QRadioData *radioData() const;

public slots:
    void searchForward();
    void searchBackward();
    void searchAllStations(QRadioTuner::SearchMode searchMode = QRadioTuner::SearchFast);
    void cancelSearch();
    void setBand(QRadioTuner::Band band);
    void setFrequency(int frequency);
    void setVolume(int volume);
    void setMuted(bool muted);
    void start();
    void stop();

signals:
    void stateChanged(QRadioTuner::State state);
    void bandChanged(QRadioTuner::Band band);
    void frequencyChanged(int frequency);
    void stereoStatusChanged(bool stereo);
    void searchingChanged(bool searching);
    void signalStrengthChanged(int signalStrength);
    void volumeChanged(int volume);
    void mutedChanged(bool muted);
    void stationFound(int frequency, QString stationId);
    void antennaConnectedChanged(bool connectionStatus);
    void error(QRadioTuner::Error error);
};
