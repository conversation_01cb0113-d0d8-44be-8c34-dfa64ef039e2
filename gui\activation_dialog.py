from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QLineEdit, QMessageBox, QFrame,
                            QTextEdit, QScrollArea, QWidget)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
import json
import os
from datetime import datetime
from PyQt5.QtWidgets import QShortcut
from PyQt5.QtGui import QKeySequence
from license_manager import LicenseManager
import hashlib

class ActivationDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.license_manager = LicenseManager()
        self.setup_ui()
        self.load_current_status()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🔑 تفعيل البرنامج")
        self.setFixedSize(900, 1200)  # تكبير الطول بنسبة 100%
        self.setModal(True)

        # العنوان في المنتصف فقط
        title_label = QLabel("🔑 تفعيل البرنامج")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: white;
            margin-bottom: 20px;
        """)
        title_bar = QHBoxLayout()
        title_bar.setContentsMargins(10, 10, 10, 0)
        title_bar.addWidget(title_label)

        # تطبيق التصميم
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #141E30, stop:1 #243B55);
                border-radius: 15px;
            }
            QFrame {
                background-color: rgba(255, 255, 255, 0.95);
                border-radius: 12px;
                padding: 20px;
            }
            QLabel {
                color: #2c3e50;
                font-size: 16px;
            }
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 10px;
                font-size: 16px;
                background: white;
                min-height: 20px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2980b9, stop:1 #1f618d);
            }
            QPushButton:pressed {
                background: #1f618d;
            }
        """)

        main_layout = QVBoxLayout(self)
        main_layout.addLayout(title_bar)

        layout = QVBoxLayout()
        layout.setSpacing(20)
        layout.setContentsMargins(30, 10, 30, 30)

        # بيانات كود العميل واسم الجهاز (ظاهرة دائماً)
        customer_code = self.license_manager.get_customer_code()
        machine_id = self.license_manager.get_machine_id()
        info_box = QFrame()
        info_box.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border: 2px solid #2980b9;
                border-radius: 10px;
                padding: 18px;
                margin-bottom: 10px;
            }
        """)
        info_layout = QVBoxLayout(info_box)
        info_layout.setSpacing(8)
        # صف كود العميل مع زر نسخ
        code_row = QHBoxLayout()
        code_label = QLabel(
            "🔑 كود العميل: "
            f"<span style='color:#1a237e;font-weight:bold'>"
            f"{customer_code}" "</span>"
        )
        code_label.setTextFormat(Qt.RichText)
        code_label.setStyleSheet(
            "font-size: 18px; color: #1a237e; font-weight: bold;"
        )
        code_row.addWidget(code_label)
        copy_code_btn = QPushButton("📋")
        copy_code_btn.setFixedSize(32, 32)
        copy_code_btn.setToolTip("نسخ كود العميل")
        copy_code_btn.clicked.connect(lambda: self.copy_single_value(customer_code, 'كود العميل'))
        code_row.addWidget(copy_code_btn)
        code_row.addStretch()
        info_layout.addLayout(code_row)
        # صف اسم الجهاز مع زر نسخ
        machine_row = QHBoxLayout()
        machine_label = QLabel(
            "💻 اسم الجهاز: "
            f"<span style='color:#00695c;font-weight:bold'>"
            f"{machine_id}" "</span>"
        )
        machine_label.setTextFormat(Qt.RichText)
        machine_label.setStyleSheet(
            "font-size: 18px; color: #00695c; font-weight: bold;"
        )
        machine_row.addWidget(machine_label)
        copy_machine_btn = QPushButton("📋")
        copy_machine_btn.setFixedSize(32, 32)
        copy_machine_btn.setToolTip("نسخ اسم الجهاز")
        copy_machine_btn.clicked.connect(lambda: self.copy_single_value(machine_id, 'اسم الجهاز'))
        machine_row.addWidget(copy_machine_btn)
        machine_row.addStretch()
        info_layout.addLayout(machine_row)
        layout.addWidget(info_box)

        # إطار المحتوى
        content_frame = QFrame()
        content_layout = QVBoxLayout(content_frame)

        # حالة التفعيل الحالية
        self.status_label = QLabel()
        self.status_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
        """)
        content_layout.addWidget(self.status_label)

        # حقل كود التفعيل
        activation_layout = QHBoxLayout()
        activation_layout.addWidget(QLabel("كود التفعيل:"))
        self.activation_code = QLineEdit()
        self.activation_code.setPlaceholderText("أدخل كود التفعيل هنا...")
        activation_layout.addWidget(self.activation_code)
        content_layout.addLayout(activation_layout)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        # زر نسخ بيانات الجهاز
        copy_data_btn = QPushButton("📋 نسخ بيانات الجهاز")
        copy_data_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        copy_data_btn.clicked.connect(self.copy_machine_data)
        buttons_layout.addWidget(copy_data_btn)

        # زر التفعيل
        self.activate_button = QPushButton("✅ تفعيل")
        self.activate_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)
        self.activate_button.clicked.connect(self.activate_license)
        buttons_layout.addWidget(self.activate_button)

        content_layout.addLayout(buttons_layout)
        layout.addWidget(content_frame)

        main_layout.addLayout(layout)
        self.setLayout(main_layout)

        # إضافة اختصار لوحة المفاتيح للشاشة الكاملة (F11)
        self.fullscreen_shortcut = QShortcut(QKeySequence("F11"), self)
        self.fullscreen_shortcut.activated.connect(self.toggle_fullscreen)
        
    def load_current_status(self):
        """تحميل حالة التفعيل الحالية من نظام التراخيص الجديد"""
        try:
            # فحص الترخيص باستخدام النظام الجديد
            license_status = self.license_manager.check_license()
            customer_code = self.license_manager.get_customer_code()
            machine_id = self.license_manager.get_machine_id()

            if license_status["valid"]:
                # البرنامج مفعل
                expiry_date = license_status['expiry_date']
                days_remaining = license_status['days_remaining']

                # تحديد لون التحذير حسب الأيام المتبقية
                if days_remaining <= 7:
                    color = "#e74c3c"  # أحمر للتحذير
                    warning = "⚠️ "
                elif days_remaining <= 30:
                    color = "#f39c12"  # برتقالي للتنبيه
                    warning = "⚠️ "
                else:
                    color = "#27ae60"  # أخضر للحالة الطبيعية
                    warning = ""

                status_text = f"""
                <div style='text-align: center;'>
                    <h2 style='color: {color}; margin-bottom: 15px;'>
                        {warning}✅ البرنامج مفعل
                    </h2>

                    <div style='background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>
                        <h3 style='color: #2c3e50; margin-bottom: 10px;'>📋 معلومات الترخيص:</h3>

                        <p style='margin: 8px 0;'>
                            <strong>🔑 كود العميل:</strong>
                            <span style='color: #2980b9; font-family: monospace;'>{customer_code}</span>
                        </p>

                        <p style='margin: 8px 0;'>
                            <strong>💻 رقم الجهاز:</strong>
                            <span style='color: #2980b9; font-family: monospace;'>{machine_id}</span>
                        </p>

                        <p style='margin: 8px 0;'>
                            <strong>📅 تاريخ الانتهاء:</strong>
                            <span style='color: #e74c3c; font-weight: bold;'>{expiry_date.strftime('%d/%m/%Y')}</span>
                        </p>

                        <p style='margin: 8px 0;'>
                            <strong>⏰ الأيام المتبقية:</strong>
                            <span style='color: {color}; font-weight: bold;'>{days_remaining} يوم</span>
                        </p>

                        <p style='margin: 8px 0;'>
                            <strong>📊 حالة الترخيص:</strong>
                            <span style='color: #27ae60; font-weight: bold;'>نشط</span>
                        </p>
                    </div>

                    <div style='background-color: #e8f5e8; padding: 10px; border-radius: 8px; margin-top: 15px;'>
                        <p style='color: #27ae60; margin: 0;'>
                            <strong>🎉 البرنامج مفعل ويعمل بشكل طبيعي</strong>
                        </p>
                    </div>
                </div>
                """

                if days_remaining <= 7:
                    status_text += f"""
                    <div style='background-color: #fdf2f2; padding: 10px; border-radius: 8px; margin-top: 10px; border-left: 4px solid #e74c3c;'>
                        <p style='color: #e74c3c; margin: 0;'>
                            <strong>⚠️ تحذير: الترخيص سينتهي خلال {days_remaining} أيام!</strong><br>
                            يرجى التواصل مع المطور لتجديد الترخيص.
                        </p>
                    </div>
                    """

                self.status_label.setText(status_text)

                # إخفاء حقل إدخال كود التفعيل
                self.activation_code.setVisible(False)
                self.activate_button.setText("🔄 تجديد الترخيص")

            else:
                # البرنامج غير مفعل
                status_text = f"""
                <div style='text-align: center;'>
                    <h2 style='color: #e74c3c; margin-bottom: 15px;'>
                        🔒 البرنامج غير مفعل
                    </h2>

                    <div style='background-color: #fdf2f2; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #e74c3c;'>
                        <h3 style='color: #e74c3c; margin-bottom: 10px;'>⚠️ حالة الترخيص:</h3>
                        <p style='color: #e74c3c; margin: 5px 0;'><strong>{license_status['message']}</strong></p>
                    </div>

                    <div style='background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>
                        <h3 style='color: #2c3e50; margin-bottom: 10px;'>📋 بيانات الجهاز:</h3>

                        <p style='margin: 8px 0;'>
                            <strong>🔑 كود العميل:</strong>
                            <span style='color: #2980b9; font-family: monospace;'>{customer_code}</span>
                        </p>

                        <p style='margin: 8px 0;'>
                            <strong>💻 رقم الجهاز:</strong>
                            <span style='color: #2980b9; font-family: monospace;'>{machine_id}</span>
                        </p>
                    </div>

                    <div style='background-color: #e3f2fd; padding: 15px; border-radius: 8px; margin: 10px 0;'>
                        <h3 style='color: #1976d2; margin-bottom: 10px;'>📞 للحصول على كود التفعيل:</h3>
                        <p style='color: #1976d2; margin: 5px 0;'>
                            1️⃣ أرسل البيانات أعلاه إلى المطور<br>
                            2️⃣ البريد الإلكتروني: <strong><EMAIL></strong><br>
                            3️⃣ ستحصل على كود التفعيل خلال 24 ساعة<br>
                            4️⃣ أدخل الكود في الحقل أدناه واضغط "تفعيل"
                        </p>
                    </div>
                </div>
                """

                self.status_label.setText(status_text)

                # إظهار حقل إدخال كود التفعيل
                self.activation_code.setVisible(True)
                self.activate_button.setText("🔑 تفعيل البرنامج")

        except Exception as e:
            self.status_label.setText(f"⚠️ خطأ في تحميل حالة التفعيل: {str(e)}")
            
    def activate_license(self):
        """تفعيل البرنامج باستخدام النظام الجديد"""
        # فحص الترخيص الحالي أولاً
        current_status = self.license_manager.check_license()

        if current_status["valid"]:
            # البرنامج مفعل بالفعل - عرض خيارات التجديد
            reply = QMessageBox.question(
                self,
                "البرنامج مفعل",
                "البرنامج مفعل بالفعل.\n\nهل تريد تجديد الترخيص؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

        # الحصول على كود التفعيل
        activation_code = self.activation_code.text().strip()
        if not activation_code:
            QMessageBox.warning(self, "تنبيه", "يرجى إدخال كود التفعيل")
            return

        # الحصول على بيانات الجهاز
        customer_code = self.license_manager.get_customer_code()
        machine_id = self.license_manager.get_machine_id()

        # التحقق من صحة الكود باستخدام النظام الجديد
        validation_result = self.validate_license_code(activation_code, customer_code, machine_id)

        if not validation_result["valid"]:
            QMessageBox.critical(self, "كود غير صحيح", validation_result["message"])
            return

        # حفظ الترخيص الجديد
        try:
            if self.save_license(validation_result["license_data"]):
                # عرض رسالة نجاح التفعيل
                expiry_date = validation_result['expiry_date']
                days_remaining = validation_result['days_remaining']

                success_msg = f"""
🎉 تم تفعيل البرنامج بنجاح!

📋 تفاصيل التفعيل:
🔑 كود العميل: {customer_code}
💻 رقم الجهاز: {machine_id}
📅 صالح حتى: {expiry_date.strftime('%d/%m/%Y')}
⏰ مدة التفعيل: {days_remaining} يوم

✅ البرنامج الآن مفعل ويعمل بشكل طبيعي.
                """

                QMessageBox.information(self, "تم التفعيل بنجاح! 🎉", success_msg)

                # تحديث واجهة المستخدم
                self.load_current_status()

            else:
                QMessageBox.critical(self, "خطأ", "فشل في حفظ الترخيص")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء التفعيل:\n{str(e)}")

    def validate_license_code(self, license_code, customer_code, machine_id):
        """التحقق من صحة كود الترخيص"""
        try:
            # تنسيق الكود المتوقع: SICOO-YYYYMMDD-CUSTOMER-MACHINE-HASH
            parts = license_code.strip().upper().split('-')

            if len(parts) != 5 or parts[0] != "SICOO":
                return {
                    "valid": False,
                    "message": "تنسيق كود الترخيص غير صحيح"
                }

            date_str = parts[1]
            customer_part = parts[2]
            machine_part = parts[3]
            hash_part = parts[4]

            # التحقق من التاريخ
            try:
                expiry_date = datetime.strptime(date_str, "%Y%m%d")
                current_date = datetime.now()

                if current_date > expiry_date:
                    return {
                        "valid": False,
                        "message": "كود الترخيص منتهي الصلاحية"
                    }

                days_remaining = (expiry_date - current_date).days

            except ValueError:
                return {
                    "valid": False,
                    "message": "تاريخ انتهاء الترخيص غير صحيح"
                }

            # التحقق من كود العميل ورقم الجهاز
            expected_customer = customer_code[:6].upper()
            expected_machine = machine_id[:6].upper()

            if customer_part != expected_customer:
                return {
                    "valid": False,
                    "message": "كود الترخيص غير صالح لهذا العميل"
                }

            if machine_part != expected_machine:
                return {
                    "valid": False,
                    "message": "كود الترخيص غير صالح لهذا الجهاز"
                }

            # التحقق من الـ hash
            data_to_hash = f"{customer_code}{machine_id}{date_str}SICOO_LICENSE_2024"
            expected_hash = hashlib.sha256(data_to_hash.encode()).hexdigest()[:8].upper()

            if hash_part != expected_hash:
                return {
                    "valid": False,
                    "message": "كود الترخيص غير صحيح أو تالف"
                }

            # إنشاء بيانات الترخيص
            license_data = {
                "customer_code": customer_code,
                "machine_id": machine_id,
                "expiry_date": expiry_date.isoformat(),
                "license_type": "FULL",
                "license_code": license_code,
                "activated_date": datetime.now().isoformat()
            }

            return {
                "valid": True,
                "message": "كود الترخيص صحيح",
                "expiry_date": expiry_date,
                "days_remaining": days_remaining,
                "license_data": license_data
            }

        except Exception as e:
            return {
                "valid": False,
                "message": f"خطأ في التحقق من الكود: {str(e)}"
            }

    def save_license(self, license_data):
        """حفظ بيانات الترخيص"""
        try:
            return self.license_manager._save_license(license_data)
        except Exception as e:
            print(f"خطأ في حفظ الترخيص: {e}")
            return False

    def copy_machine_data(self):
        """نسخ بيانات الجهاز إلى الحافظة"""
        try:
            import pyperclip
            customer_code = self.license_manager.get_customer_code()
            machine_id = self.license_manager.get_machine_id()

            data = f"""بيانات الجهاز للتفعيل:

🔑 كود العميل: {customer_code}
💻 رقم الجهاز: {machine_id}

يرجى إرسال هذه البيانات إلى:
📧 <EMAIL>

للحصول على كود التفعيل."""

            pyperclip.copy(data)
            QMessageBox.information(
                self,
                "تم النسخ",
                "تم نسخ بيانات الجهاز إلى الحافظة\n\n"
                "يمكنك الآن لصقها في الإيميل وإرسالها للمطور"
            )
        except ImportError:
            # إذا لم تكن pyperclip متوفرة، عرض البيانات في نافذة
            customer_code = self.license_manager.get_customer_code()
            machine_id = self.license_manager.get_machine_id()

            data = f"""بيانات الجهاز للتفعيل:

🔑 كود العميل: {customer_code}
💻 رقم الجهاز: {machine_id}

يرجى إرسال هذه البيانات إلى:
📧 <EMAIL>"""

            QMessageBox.information(self, "بيانات الجهاز", data)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في نسخ البيانات: {str(e)}")

    def toggle_fullscreen(self):
        """تبديل وضع الشاشة الكاملة"""
        if self.isFullScreen():
            self.showNormal()
            self.fullscreen_btn.setText("🔳")
            self.fullscreen_btn.setToolTip("فتح في شاشة كاملة (F11)")
        else:
            self.showFullScreen()
            self.fullscreen_btn.setText("🗗")
            self.fullscreen_btn.setToolTip("العودة للحجم العادي (F11)")