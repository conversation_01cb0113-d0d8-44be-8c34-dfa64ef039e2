#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملف Excel تجريبي يحتوي على أكواد مكررة لاختبار معالجة التكرار
"""

import pandas as pd
from datetime import datetime

def create_duplicate_test_excel():
    """إنشاء ملف Excel يحتوي على أكواد مكررة"""
    
    # بيانات تجريبية مع أكواد مكررة
    products_data = [
        {
            'Name': 'منتج أول بكود مكرر',
            'Code': 'DUP001',
            'Description': 'المنتج الأول بكود مكرر',
            'Category': 'فئة تجريبية',
            'Purchase_Price': 100.0,
            'Sale_Price': 150.0,
            'Quantity': 10,
            'Unit': 'قطعة',
            'Barcode': '1111111111111'
        },
        {
            'Name': 'منتج ثاني بنفس الكود',
            'Code': 'DUP001',  # نفس الكود السابق
            'Description': 'المنتج الثاني بنفس الكود',
            'Category': 'فئة تجريبية',
            'Purchase_Price': 200.0,
            'Sale_Price': 300.0,
            'Quantity': 5,
            'Unit': 'قطعة',
            'Barcode': '2222222222222'
        },
        {
            'Name': 'منتج ثالث بنفس الكود',
            'Code': 'DUP001',  # نفس الكود مرة أخرى
            'Description': 'المنتج الثالث بنفس الكود',
            'Category': 'فئة أخرى',
            'Purchase_Price': 50.0,
            'Sale_Price': 75.0,
            'Quantity': 20,
            'Unit': 'قطعة',
            'Barcode': '3333333333333'
        },
        {
            'Name': 'منتج بكود فريد',
            'Code': 'UNIQUE001',
            'Description': 'منتج بكود فريد',
            'Category': 'فئة تجريبية',
            'Purchase_Price': 30.0,
            'Sale_Price': 45.0,
            'Quantity': 15,
            'Unit': 'قطعة',
            'Barcode': '4444444444444'
        },
        {
            'Name': 'منتج آخر بكود مكرر',
            'Code': 'DUP002',
            'Description': 'منتج آخر بكود مكرر',
            'Category': 'فئة تجريبية',
            'Purchase_Price': 40.0,
            'Sale_Price': 60.0,
            'Quantity': 8,
            'Unit': 'قطعة',
            'Barcode': '5555555555555'
        },
        {
            'Name': 'منتج ثاني بكود مكرر آخر',
            'Code': 'DUP002',  # نفس الكود السابق
            'Description': 'منتج ثاني بكود مكرر آخر',
            'Category': 'فئة تجريبية',
            'Purchase_Price': 60.0,
            'Sale_Price': 90.0,
            'Quantity': 12,
            'Unit': 'قطعة',
            'Barcode': '6666666666666'
        },
        {
            'Name': 'منتج بدون كود 1',
            'Code': '',  # كود فارغ
            'Description': 'منتج بدون كود الأول',
            'Category': 'فئة تجريبية',
            'Purchase_Price': 25.0,
            'Sale_Price': 40.0,
            'Quantity': 18,
            'Unit': 'قطعة',
            'Barcode': '7777777777777'
        },
        {
            'Name': 'منتج بدون كود 2',
            'Code': '',  # كود فارغ آخر
            'Description': 'منتج بدون كود الثاني',
            'Category': 'فئة تجريبية',
            'Purchase_Price': 35.0,
            'Sale_Price': 55.0,
            'Quantity': 22,
            'Unit': 'قطعة',
            'Barcode': '8888888888888'
        },
        {
            'Name': 'منتج بمسافات في الكود',
            'Code': '  SPACE001  ',  # كود بمسافات
            'Description': 'منتج بمسافات في الكود',
            'Category': 'فئة تجريبية',
            'Purchase_Price': 45.0,
            'Sale_Price': 70.0,
            'Quantity': 14,
            'Unit': 'قطعة',
            'Barcode': '9999999999999'
        },
        {
            'Name': 'منتج آخر بنفس الكود بعد تنظيف المسافات',
            'Code': 'SPACE001',  # نفس الكود بدون مسافات
            'Description': 'منتج آخر بنفس الكود',
            'Category': 'فئة تجريبية',
            'Purchase_Price': 55.0,
            'Sale_Price': 85.0,
            'Quantity': 16,
            'Unit': 'قطعة',
            'Barcode': '1010101010101'
        }
    ]
    
    # إنشاء DataFrame
    df = pd.DataFrame(products_data)
    
    # حفظ في ملف Excel
    filename = f"test_duplicate_products_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    df.to_excel(filename, index=False, engine='openpyxl')
    
    print(f"✅ تم إنشاء ملف Excel للاختبار: {filename}")
    print(f"📊 عدد المنتجات: {len(products_data)}")
    print("\n📋 تحليل الأكواد:")
    
    # تحليل الأكواد
    codes = [p['Code'].strip() if p['Code'] else '[فارغ]' for p in products_data]
    code_counts = {}
    for code in codes:
        code_counts[code] = code_counts.get(code, 0) + 1
    
    for code, count in code_counts.items():
        if count > 1:
            print(f"  🔄 الكود '{code}' مكرر {count} مرة")
        else:
            print(f"  ✅ الكود '{code}' فريد")
    
    print(f"\n📋 المنتجات:")
    for i, product in enumerate(products_data, 1):
        code_display = product['Code'].strip() if product['Code'].strip() else '[فارغ]'
        print(f"  {i}. {product['Name']} - كود: '{code_display}'")
    
    print(f"\n🎯 هذا الملف سيختبر:")
    print("  • معالجة الأكواد المكررة داخل نفس الملف")
    print("  • تنظيف المسافات من الأكواد")
    print("  • التعامل مع الأكواد الفارغة")
    print("  • إنشاء أكواد بديلة للمكررات")
    
    return filename

if __name__ == "__main__":
    create_duplicate_test_excel()
