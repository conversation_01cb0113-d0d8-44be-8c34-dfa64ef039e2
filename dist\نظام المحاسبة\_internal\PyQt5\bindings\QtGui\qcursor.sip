// qcursor.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QCursor /TypeHintIn="Union[QCursor, Qt.CursorShape]"/
{
%TypeHeaderCode
#include <qcursor.h>
%End

%ConvertToTypeCode
// SIP doesn't support automatic type convertors so we explicitly allow a
// Qt::CursorShape to be used whenever a QCursor is expected.

if (sipIsErr == NULL)
    return (PyObject_TypeCheck(sipPy, sipTypeAsPyTypeObject(sipType_Qt_CursorShape)) ||
            sipCanConvertToType(sipPy, sipType_QCursor, SIP_NO_CONVERTORS));

if (PyObject_TypeCheck(sipPy, sipTypeAsPyTypeObject(sipType_Qt_CursorShape)))
{
    *sipCppPtr = new QCursor((Qt::CursorShape)SIPLong_AsLong(sipPy));

    return sipGetState(sipTransferObj);
}

*sipCppPtr = reinterpret_cast<QCursor *>(sipConvertToType(sipPy, sipType_QCursor, sipTransferObj, SIP_NO_CONVERTORS, 0, sipIsErr));

return 0;
%End

public:
    QCursor();
    QCursor(const QBitmap &bitmap, const QBitmap &mask, int hotX = -1, int hotY = -1);
    QCursor(const QPixmap &pixmap, int hotX = -1, int hotY = -1);
    QCursor(const QCursor &cursor);
    QCursor(const QVariant &variant /GetWrapper/) /NoDerived/;
%MethodCode
        if (a0->canConvert<QCursor>())
            sipCpp = new QCursor(a0->value<QCursor>());
        else
            sipError = sipBadCallableArg(0, a0Wrapper);
%End

    ~QCursor();
    Qt::CursorShape shape() const;
    void setShape(Qt::CursorShape newShape);
    const QBitmap *bitmap() const;
    const QBitmap *mask() const;
    QPixmap pixmap() const;
    QPoint hotSpot() const;
    static QPoint pos();
    static void setPos(int x, int y);
    static void setPos(const QPoint &p);
    static QPoint pos(const QScreen *screen);
    static void setPos(QScreen *screen, int x, int y);
    static void setPos(QScreen *screen, const QPoint &p);
%If (Qt_5_7_0 -)
    void swap(QCursor &other);
%End
};

QDataStream &operator<<(QDataStream &outS, const QCursor &cursor /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &inS, QCursor &cursor /Constrained/) /ReleaseGIL/;
%If (Qt_5_10_0 -)
bool operator==(const QCursor &lhs, const QCursor &rhs);
%End
%If (Qt_5_10_0 -)
bool operator!=(const QCursor &lhs, const QCursor &rhs);
%End
