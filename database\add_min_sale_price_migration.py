#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة عمود min_sale_price لجدول products
"""

from sqlalchemy import create_engine, text
import os

def add_min_sale_price_column():
    """إضافة عمود الحد الأدنى للبيع لجدول المنتجات"""
    
    # مسار قاعدة البيانات
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'accounting.db')
    engine = create_engine(f'sqlite:///{db_path}')
    
    try:
        with engine.connect() as conn:
            # التحقق من وجود العمود أولاً
            result = conn.execute(text("PRAGMA table_info(products)"))
            columns = [row[1] for row in result.fetchall()]
            
            if 'min_sale_price' not in columns:
                print("🔄 إضافة عمود min_sale_price...")
                
                # إضافة العمود الجديد
                conn.execute(text("ALTER TABLE products ADD COLUMN min_sale_price REAL DEFAULT 0"))
                
                # تحديث القيم الموجودة لتكون الحد الأدنى = سعر الشراء
                conn.execute(text("""
                    UPDATE products 
                    SET min_sale_price = purchase_price 
                    WHERE min_sale_price IS NULL OR min_sale_price = 0
                """))
                
                conn.commit()
                print("✅ تم إضافة عمود min_sale_price بنجاح!")
                print("📝 تم تعيين الحد الأدنى للبيع = سعر الشراء للمنتجات الموجودة")
                
            else:
                print("ℹ️ عمود min_sale_price موجود بالفعل")
                
    except Exception as e:
        print(f"❌ خطأ في إضافة العمود: {e}")
        return False
        
    return True

if __name__ == "__main__":
    add_min_sale_price_column()
